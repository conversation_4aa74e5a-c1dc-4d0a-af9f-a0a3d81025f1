import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../providers/seller_provider.dart';
import '../utils/app_colors.dart';
import '../utils/dimens.dart';

/// 통합 필터 다이얼로그
/// 
/// 판매자 선택과 기간 선택을 하나의 다이얼로그에서 처리합니다.
/// - 현대적인 Material Design 3 스타일
/// - 앱의 전체적인 디자인과 일관성 유지
/// - 사용자 친화적인 인터페이스
class UnifiedFilterDialog extends ConsumerStatefulWidget {
  final String initialSeller;
  final DateTimeRange? initialDateRange;

  const UnifiedFilterDialog({
    super.key,
    required this.initialSeller,
    this.initialDateRange,
  });

  @override
  ConsumerState<UnifiedFilterDialog> createState() => _UnifiedFilterDialogState();
}

class _UnifiedFilterDialogState extends ConsumerState<UnifiedFilterDialog> {
  late String _selectedSeller;
  DateTimeRange? _selectedDateRange;
  final DateRangePickerController _dateController = DateRangePickerController();

  @override
  void initState() {
    super.initState();
    _selectedSeller = widget.initialSeller;
    _selectedDateRange = widget.initialDateRange;
    
    // 초기 날짜 범위 설정
    if (_selectedDateRange != null) {
      _dateController.selectedRange = PickerDateRange(
        _selectedDateRange!.start,
        _selectedDateRange!.end,
      );
    }
  }

  @override
  void dispose() {
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final sellerAsync = ref.watch(sellerNotifierProvider);
    final allSellersOption = '전체 판매자';
    final sellerNames = sellerAsync.isLoading
        ? <String>[]
        : sellerAsync.hasError
            ? <String>[]
            : sellerAsync.sellers.map((s) => s.name).toList()..sort();
    final sellers = [allSellersOption, ...sellerNames];

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(Dimens.radiusL),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxHeight: 600),
        padding: const EdgeInsets.all(Dimens.space24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 제목
            Text(
              '필터 설정',
              style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                fontFamily: 'Pretendard',
                fontWeight: FontWeight.bold,
                color: AppColors.primarySeed,
              ),
            ),
            const SizedBox(height: Dimens.space24),

            // 판매자 선택 섹션
            _buildSellerSection(sellers),
            const SizedBox(height: Dimens.space24),

            // 기간 선택 섹션
            _buildDateRangeSection(),
            const SizedBox(height: Dimens.space32),

            // 버튼 영역
            _buildButtonSection(),
          ],
        ),
      ),
    );
  }

  /// 판매자 선택 섹션
  Widget _buildSellerSection(List<String> sellers) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '판매자',
          style: Theme.of(context).textTheme.titleMedium!.copyWith(
            fontFamily: 'Pretendard',
            fontWeight: FontWeight.w600,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: Dimens.space12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: Dimens.space16),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.neutral30),
            borderRadius: BorderRadius.circular(Dimens.radiusM),
            color: AppColors.surface,
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedSeller,
              isExpanded: true,
              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                fontFamily: 'Pretendard',
                color: AppColors.onSurface,
              ),
              icon: Icon(
                Icons.arrow_drop_down,
                color: AppColors.neutral60,
              ),
              items: sellers.map((seller) {
                return DropdownMenuItem<String>(
                  value: seller,
                  child: Text(seller),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedSeller = value;
                  });
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  /// 기간 선택 섹션
  Widget _buildDateRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '기간',
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                fontFamily: 'Pretendard',
                fontWeight: FontWeight.w600,
                color: AppColors.onSurface,
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedDateRange = null;
                  _dateController.selectedRange = null;
                });
              },
              child: Text(
                '전체 기간',
                style: TextStyle(
                  fontFamily: 'Pretendard',
                  color: AppColors.primarySeed,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: Dimens.space12),
        Container(
          height: 300,
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.neutral30),
            borderRadius: BorderRadius.circular(Dimens.radiusM),
            color: AppColors.surface,
          ),
          child: SfDateRangePicker(
            controller: _dateController,
            view: DateRangePickerView.month,
            selectionMode: DateRangePickerSelectionMode.range,
            showActionButtons: false,
            headerStyle: DateRangePickerHeaderStyle(
              textStyle: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.primarySeed,
              ),
            ),
            monthViewSettings: DateRangePickerMonthViewSettings(
              viewHeaderStyle: DateRangePickerViewHeaderStyle(
                textStyle: TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppColors.neutral60,
                ),
              ),
            ),
            selectionTextStyle: TextStyle(
              fontFamily: 'Pretendard',
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
            rangeTextStyle: TextStyle(
              fontFamily: 'Pretendard',
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
            monthCellStyle: DateRangePickerMonthCellStyle(
              textStyle: TextStyle(
                fontFamily: 'Pretendard',
                color: AppColors.onSurface,
              ),
            ),
            onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
              if (args.value is PickerDateRange) {
                final range = args.value as PickerDateRange;
                if (range.startDate != null && range.endDate != null) {
                  setState(() {
                    _selectedDateRange = DateTimeRange(
                      start: range.startDate!,
                      end: range.endDate!,
                    );
                  });
                }
              }
            },
          ),
        ),
      ],
    );
  }

  /// 버튼 섹션
  Widget _buildButtonSection() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: Dimens.space12),
              side: BorderSide(color: AppColors.neutral30),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(Dimens.radiusM),
              ),
            ),
            child: Text(
              '취소',
              style: TextStyle(
                fontFamily: 'Pretendard',
                color: AppColors.neutral60,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        const SizedBox(width: Dimens.space16),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop({
                'seller': _selectedSeller,
                'dateRange': _selectedDateRange,
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primarySeed,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: Dimens.space12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(Dimens.radiusM),
              ),
            ),
            child: const Text(
              '적용',
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
