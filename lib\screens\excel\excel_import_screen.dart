import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import '../../models/prepayment_virtual_product.dart';

import '../../utils/excel_processor.dart';
import '../../providers/settings_provider.dart';
import '../../providers/prepayment_provider.dart';
import '../../providers/prepayment_virtual_product_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../models/prepayment.dart';
import '../../providers/prepayment_product_link_provider.dart';
import '../../services/database_service.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';

import '../../widgets/excel_import_mode_dialog.dart';

import '../../widgets/registration_complete_page.dart';
import '../../widgets/registration_link_prompt_page.dart';
import '../inventory/inventory_screen.dart';
import '../../services/link_service.dart';
import '../../utils/app_colors.dart';

// 등록 모드 열거형
enum RegistrationMode {
  prepaymentOnly('선입금만 등록', '선입금 데이터만 등록합니다', Icons.account_balance_wallet_rounded);

  const RegistrationMode(this.title, this.description, this.icon);

  final String title;
  final String description;
  final IconData icon;
}

/// 엑셀 파일을 통한 선입금 데이터 등록 화면입니다.
class ExcelImportScreen extends ConsumerStatefulWidget {
  final RegistrationMode? initialMode;
  final bool? initialCollectDayOfWeek;
  final int? initialDayOfWeekColumnIndex;

  const ExcelImportScreen({
    super.key,
    this.initialMode,
    this.initialCollectDayOfWeek,
    this.initialDayOfWeekColumnIndex,
  });

  @override
  ConsumerState<ExcelImportScreen> createState() => _ExcelImportScreenState();
}

class _ExcelImportScreenState extends ConsumerState<ExcelImportScreen> {
  List<ExcelPrepaymentData> _prepaymentData = [];
  bool _isLoading = false;
  bool _isProcessing = false;
  String? _selectedFileName;
  
  // [개선] 등록 모드 선택
  late RegistrationMode _selectedMode;
  
  // 상품 가격 입력을 위한 컨트롤러들
  final Map<String, TextEditingController> _priceControllers = {};

  @override
  void initState() {
    super.initState();
    // 진입 파라미터 반영
    final mode = widget.initialMode ?? RegistrationMode.prepaymentOnly;
    _selectedMode = mode;
    // 요일수집 관련 파라미터는 Provider에서 처리(별도 구현 필요시 추가)
    // 앱 실행 시점에 prepayment_virtual_product 테이블이 없으면 생성 (안전망)
    final databaseService = ref.read(databaseServiceProvider);
    if (databaseService is DatabaseServiceImpl) {
      databaseService.ensurePrepaymentVirtualProductTable();
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '엑셀로 일괄 등록',
              style: TextStyle(
                color: AppColors.onboardingTextOnPrimary,
                fontSize: isTablet ? 20.0 : 18.0,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              _selectedMode.title,
              style: TextStyle(
                color: AppColors.onboardingTextOnPrimary.withValues(alpha: 0.8),
                fontSize: isTablet ? 14.0 : 12.0,
              ),
            ),
          ],
        ),
        centerTitle: true,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.onboardingTextOnPrimary,
        elevation: 0,
        actions: [
          Container(
            margin: EdgeInsets.only(right: isTablet ? 16.0 : 8.0),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.onboardingAccent,
                  AppColors.onboardingAccentLight,
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppColors.onboardingAccent.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: _showModeSelector,
                child: Container(
                  padding: EdgeInsets.all(isTablet ? 12.0 : 8.0),
                  child: Icon(
                    Icons.tune_rounded,
                    color: AppColors.onboardingTextOnPrimary,
                    size: isTablet ? 24.0 : 20.0,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 파일 선택 영역
            _buildFileSelectionArea(),
            // 선입금 데이터 리스트만 단일 화면에 표시
            if (_prepaymentData.isNotEmpty)
              Expanded(child: _buildPrepaymentList())
            else
              Expanded(child: _buildEmptyState()),
            // 액션 버튼들
            if (_prepaymentData.isNotEmpty) _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    // 가격 컨트롤러들 정리
    for (final controller in _priceControllers.values) {
      controller.dispose();
    }
    _priceControllers.clear();
    super.dispose();
  }

  /// 파일 선택 영역을 구성합니다.
  Widget _buildFileSelectionArea() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Container(
      margin: EdgeInsets.all(isTablet ? 24.0 : 16.0),
      padding: EdgeInsets.all(isTablet ? 24.0 : 20.0),
      decoration: BoxDecoration(
        gradient: AppColors.backgroundGradient,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 모드 정보 표시
          Container(
            padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.onboardingPrimary.withValues(alpha: 0.1),
                  AppColors.onboardingPrimaryLight.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.onboardingPrimary.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isTablet ? 12.0 : 8.0),
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _selectedMode.icon,
                    color: AppColors.onboardingTextOnPrimary,
                    size: isTablet ? 24.0 : 20.0,
                  ),
                ),
                SizedBox(width: isTablet ? 16.0 : 12.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _selectedMode.title,
                        style: TextStyle(
                          fontSize: isTablet ? 18.0 : 16.0,
                          fontWeight: FontWeight.bold,
                          color: AppColors.onboardingTextPrimary,
                        ),
                      ),
                      SizedBox(height: isTablet ? 6.0 : 4.0),
                      Text(
                        _selectedMode.description,
                        style: TextStyle(
                          fontSize: isTablet ? 14.0 : 12.0,
                          color: AppColors.onboardingTextSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: isTablet ? 24.0 : 20.0),

          // 파일 선택 버튼
          Container(
            decoration: BoxDecoration(
              gradient: _selectedFileName != null
                ? LinearGradient(
                    colors: [
                      AppColors.success,
                      AppColors.successLight,
                    ],
                  )
                : LinearGradient(
                    colors: [
                      AppColors.surfaceVariant,
                      AppColors.secondary.withValues(alpha: 0.3),
                    ],
                  ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: _selectedFileName != null
                  ? AppColors.success.withValues(alpha: 0.3)
                  : AppColors.secondary.withValues(alpha: 0.3),
              ),
              boxShadow: [
                BoxShadow(
                  color: _selectedFileName != null
                    ? AppColors.success.withValues(alpha: 0.2)
                    : AppColors.shadowLight,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(16),
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: _isLoading ? null : _pickExcelFile,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    vertical: isTablet ? 20.0 : 16.0,
                    horizontal: isTablet ? 24.0 : 16.0,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _selectedFileName != null
                          ? Icons.check_circle_rounded
                          : (_isLoading ? Icons.hourglass_empty_rounded : Icons.file_upload_rounded),
                        color: _selectedFileName != null
                          ? AppColors.onboardingTextOnPrimary
                          : AppColors.onboardingTextSecondary,
                        size: isTablet ? 28.0 : 24.0,
                      ),
                      SizedBox(width: isTablet ? 16.0 : 12.0),
                      Expanded(
                        child: Text(
                          _selectedFileName ?? (_isLoading ? '처리 중...' : '엑셀 파일 선택'),
                          style: TextStyle(
                            fontSize: isTablet ? 18.0 : 16.0,
                            color: _selectedFileName != null
                              ? AppColors.onboardingTextOnPrimary
                              : AppColors.onboardingTextSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 빈 상태 화면을 구성합니다.
  Widget _buildEmptyState() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Center(
      child: Container(
        margin: EdgeInsets.all(isTablet ? 32.0 : 24.0),
        padding: EdgeInsets.all(isTablet ? 48.0 : 32.0),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.surfaceVariant,
              AppColors.secondary.withValues(alpha: 0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(isTablet ? 24.0 : 20.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.onboardingPrimary.withValues(alpha: 0.2),
                    AppColors.onboardingPrimaryLight.withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                Icons.description_rounded,
                size: isTablet ? 80.0 : 64.0,
                color: AppColors.onboardingPrimary,
              ),
            ),
            SizedBox(height: isTablet ? 24.0 : 20.0),
            Text(
              '엑셀 파일을 선택해주세요',
              style: TextStyle(
                fontSize: isTablet ? 24.0 : 20.0,
                fontWeight: FontWeight.bold,
                color: AppColors.onboardingTextPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: isTablet ? 16.0 : 12.0),
            Text(
              '위치폼에서 추출한 선입금 데이터 엑셀 파일을 선택하면\n미리보기 후 일괄 등록할 수 있습니다.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: isTablet ? 18.0 : 16.0,
                color: AppColors.onboardingTextSecondary,
                height: 1.5,
              ),
            ),
            SizedBox(height: isTablet ? 24.0 : 20.0),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 20.0 : 16.0,
                vertical: isTablet ? 12.0 : 8.0,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.onboardingAccent.withValues(alpha: 0.2),
                    AppColors.onboardingAccentLight.withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    color: AppColors.onboardingAccent,
                    size: isTablet ? 20.0 : 16.0,
                  ),
                  SizedBox(width: isTablet ? 12.0 : 8.0),
                  Text(
                    '지원 형식: .xlsx, .xls',
                    style: TextStyle(
                      fontSize: isTablet ? 16.0 : 14.0,
                      color: AppColors.onboardingAccent,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 선입금 데이터 리스트를 구성합니다.
  Widget _buildPrepaymentList() {
    return Scrollbar(
      thumbVisibility: true,
      trackVisibility: true,
      child: ListView.builder(
        itemCount: _prepaymentData.length,
        itemBuilder: (context, index) {
          final data = _prepaymentData[index];
          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: ListTile(
              title: Text(
                '주문번호 ${data.orderNumber} : ${data.buyerName} ${data.totalAmount.toString()}원',
                style: Theme.of(context).textTheme.titleMedium!.copyWith(fontFamily: 'Pretendard', fontWeight: FontWeight.bold),
              ),
              trailing: IconButton(
                icon: const Icon(Icons.info_outline),
                onPressed: () => _showPrepaymentDetail(data),
                tooltip: '상세보기',
              ),
            ),
          );
        },
      ),
    );
  }

  // [신규] 등록 모드 선택 다이얼로그
  void _showModeSelector() async {
    // 현재 설정값 읽기
    final mode = _selectedMode;
    final settings = ref.read(settingsNotifierProvider).value;
    final collectDayOfWeek = settings?.collectDayOfWeekFromExcel ?? false;
    final dayOfWeekColumnIndex = settings?.excelDayOfWeekColumnIndex ?? -1;
    // 기존 커스텀 다이얼로그 → ExcelImportModeDialog로 교체
    final result = await showDialog<ExcelImportDialogResult>(
      context: context,
      builder: (context) => ExcelImportModeDialog(
        initialMode: mode,
        initialCollectDayOfWeek: collectDayOfWeek,
        initialDayOfWeekColumnIndex: dayOfWeekColumnIndex,
      ),
    );
    if (result != null) {
      setState(() {
        _selectedMode = result.selectedMode;
        // 요일수집 설정도 반영
        ref.read(settingsNotifierProvider.notifier).setCollectDayOfWeekFromExcel(result.collectDayOfWeek);
        ref.read(settingsNotifierProvider.notifier).setExcelDayOfWeekColumnIndex(result.dayOfWeekColumnIndex);
      });
      // 등록 모드는 이미 setState에서 저장됨
      ToastUtils.showSuccess(context, '${result.selectedMode.title} 모드로 설정되었습니다.');
    }
  }

  /// 액션 버튼들을 구성합니다.
  Widget _buildActionButtons() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Container(
      padding: EdgeInsets.all(isTablet ? 24.0 : 16.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            AppColors.surfaceVariant.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: Column(
        children: [
          // 선택된 모드 안내
          Container(
            padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.onboardingPrimary.withValues(alpha: 0.1),
                  AppColors.onboardingPrimaryLight.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.onboardingPrimary.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isTablet ? 10.0 : 8.0),
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _selectedMode.icon,
                    color: AppColors.onboardingTextOnPrimary,
                    size: isTablet ? 24.0 : 20.0,
                  ),
                ),
                SizedBox(width: isTablet ? 16.0 : 12.0),
                Expanded(
                  child: Text(
                    '선택된 모드: ${_selectedMode.title}',
                    style: TextStyle(
                      fontSize: isTablet ? 16.0 : 14.0,
                      fontWeight: FontWeight.bold,
                      color: AppColors.onboardingTextPrimary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: isTablet ? 20.0 : 16.0),

          // 실행 버튼
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppColors.onboardingPrimary.withValues(alpha: 0.4),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(16),
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: _isProcessing ? null : () => _executeRegistration(),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    vertical: isTablet ? 20.0 : 16.0,
                    horizontal: isTablet ? 24.0 : 16.0,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (_isProcessing)
                        SizedBox(
                          width: isTablet ? 20.0 : 16.0,
                          height: isTablet ? 20.0 : 16.0,
                          child: CircularProgressIndicator(
                            strokeWidth: 2.5,
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.onboardingTextOnPrimary),
                          ),
                        )
                      else
                        Icon(
                          _selectedMode.icon,
                          size: isTablet ? 24.0 : 20.0,
                          color: AppColors.onboardingTextOnPrimary,
                        ),
                      SizedBox(width: isTablet ? 12.0 : 8.0),
                      Text(
                        _isProcessing ? '등록 중...' : _getActionButtonText(),
                        style: TextStyle(
                          fontSize: isTablet ? 18.0 : 16.0,
                          fontWeight: FontWeight.bold,
                          color: AppColors.onboardingTextOnPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getActionButtonText() {
    switch (_selectedMode) {
      case RegistrationMode.prepaymentOnly:
        return '선입금 등록';
    }
  }

  /// 선택된 모드에 따라 등록을 실행합니다.
  Future<void> _executeRegistration() async {
    switch (_selectedMode) {
      case RegistrationMode.prepaymentOnly:
        await _registerPrepayments();
        break;
    }
  }

  /// 엑셀 파일을 선택합니다.
  Future<void> _pickExcelFile() async {
    try {
      if (!mounted) return;
      setState(() {
        _isLoading = true;
      });

      LoggerUtils.logInfo('파일 선택 시작', tag: 'ExcelImportScreen');

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        allowMultiple: false,
        withData: true, // 파일 데이터를 함께 가져오기
      );

      LoggerUtils.logInfo('파일 선택 결과: ${result?.files.length ?? 0}개 파일', tag: 'ExcelImportScreen');

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        final bytes = file.bytes;
        
        LoggerUtils.logInfo('선택된 파일: ${file.name}, 크기: ${bytes?.length ?? 0} bytes', tag: 'ExcelImportScreen');
        
        if (bytes != null && bytes.isNotEmpty) {
          await _processExcelFile(bytes);
          setState(() {
            _selectedFileName = file.name;
          });
        } else {
          LoggerUtils.logWarning('파일 bytes가 null이거나 비어있음', tag: 'ExcelImportScreen');
          ToastUtils.showError(context, '파일을 읽을 수 없습니다. 파일이 손상되었거나 지원하지 않는 형식일 수 있습니다.');
        }
      } else {
        LoggerUtils.logInfo('파일이 선택되지 않음', tag: 'ExcelImportScreen');
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '엑셀 파일 선택 중 오류',
        error: e,
        stackTrace: stackTrace,
        tag: 'ExcelImportScreen',
      );
      ToastUtils.showError(context, '파일 선택 중 오류가 발생했습니다: $e');
    } finally {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 엑셀 파일을 처리합니다.
  Future<void> _processExcelFile(Uint8List bytes) async {
    try {
      if (!mounted) return;
      setState(() {
        _isProcessing = true;
      });

      LoggerUtils.logInfo('엑셀 파일 처리 시작: ${bytes.length} bytes', tag: 'ExcelImportScreen');

      // 설정에서 엑셀 관련 옵션 가져오기 (Provider에서 직접 참조)
      final settingsState = ref.read(settingsNotifierProvider).value;
      final collectDayOfWeek = settingsState?.collectDayOfWeekFromExcel ?? false;
      final dayOfWeekColumnIndex = settingsState?.excelDayOfWeekColumnIndex ?? -1;

      LoggerUtils.logInfo('설정 로드 완료: 요일수집=$collectDayOfWeek, 요일열인덱스=$dayOfWeekColumnIndex', tag: 'ExcelImportScreen');

      // 엑셀 데이터 처리
      LoggerUtils.logInfo('ExcelProcessor.processPrepaymentExcel 호출 시작', tag: 'ExcelImportScreen');
      final prepaymentData = await ExcelProcessor.processPrepaymentExcel(
        bytes,
        collectDayOfWeek: collectDayOfWeek,
        dayOfWeekColumnIndex: dayOfWeekColumnIndex,
      );

      LoggerUtils.logInfo('선입금 데이터 추출 완료: ${prepaymentData.length}개', tag: 'ExcelImportScreen');

      setState(() {
        _prepaymentData = prepaymentData;
      });

      LoggerUtils.logInfo('UI 상태 업데이트 완료', tag: 'ExcelImportScreen');
      ToastUtils.showSuccess(context, '${prepaymentData.length}개의 데이터를 추출했습니다.');
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '엑셀 파일 처리 중 오류',
        error: e,
        stackTrace: stackTrace,
        tag: 'ExcelImportScreen',
      );
      ToastUtils.showError(context, '파일 처리 중 오류가 발생했습니다: $e');
    } finally {
      if (!mounted) return;
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// 선입금 데이터를 등록합니다.
  Future<void> _registerPrepayments() async {
    try {
      if (!mounted) return;
      setState(() {
        _isProcessing = true;
      });

      // 현재 선택된 행사 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        ToastUtils.showError(context, '현재 선택된 행사가 없습니다. 행사를 선택해주세요.');
        setState(() {
          _isProcessing = false;
        });
        return;
      }

      final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
      if (currentEvent == null) {
        ToastUtils.showError(context, '행사 정보를 불러올 수 없습니다.');
        setState(() {
          _isProcessing = false;
        });
        return;
      }

      LoggerUtils.logInfo('선입금 등록 시작 - 현재 행사: ${currentEvent.name} (ID: ${currentEvent.id ?? currentWorkspace.id})', tag: 'ExcelImportScreen');

      LoggerUtils.logInfo('선입금 등록 시작:  [36m [1m [4m [0m${_prepaymentData.length}개 데이터', tag: 'ExcelImportScreen');

      final notifier = ref.read(prepaymentNotifierProvider.notifier);
      final productNotifier = ref.read(productNotifierProvider.notifier);
      final settingsState = ref.read(settingsNotifierProvider).value;
      final linkInventory = settingsState?.linkPrepaymentToInventory ?? false;
      final databaseService = ref.read(databaseServiceProvider);
      final db = await databaseService.database;
      
      int successCount = 0;
      int errorCount = 0;
      List<Prepayment> prepaymentsToAdd = [];

      LoggerUtils.logInfo('선입금 데이터 준비 시작', tag: 'ExcelImportScreen');

      // 먼저 모든 선입금 데이터를 준비
      for (final data in _prepaymentData) {
        try {
          // 상품명 리스트 생성 (가상 상품의 이름 사용)
          final productNameList = data.purchasedProducts.keys.map((product) => product.name).join(', ');
          // 엑셀에서 읽은 금액을 그대로 사용
          final prepayment = data.toPrepayment(
            id: 0,
            amount: data.totalAmount, // 엑셀 금액 그대로
            productNameList: productNameList,
          );
          prepaymentsToAdd.add(prepayment);
          LoggerUtils.logInfo('선입금 데이터 준비 완료: ${data.orderNumber}', tag: 'ExcelImportScreen');
        } catch (e) {
          errorCount++;
          LoggerUtils.logError(
            '선입금 데이터 준비 중 오류 (주문번호: ${data.orderNumber})',
            error: e,
          );
        }
      }

      LoggerUtils.logInfo('선입금 데이터 준비 완료: ${prepaymentsToAdd.length}개 성공, $errorCount개 실패', tag: 'ExcelImportScreen');

      // 배치로 선입금 등록
      if (prepaymentsToAdd.isNotEmpty) {
        LoggerUtils.logInfo('선입금 등록 시작: ${prepaymentsToAdd.length}개', tag: 'ExcelImportScreen');
        final repository = ref.read(prepaymentRepositoryProvider);
        for (int i = 0; i < prepaymentsToAdd.length; i++) {
          final prepayment = prepaymentsToAdd[i];
          try {
            LoggerUtils.logInfo('선입금 등록 중: ${i + 1}/${prepaymentsToAdd.length} - ${prepayment.buyerName}', tag: 'ExcelImportScreen');
            // 현재 워크스페이스 ID를 eventId로 설정
            final prepaymentWithEventId = prepayment.copyWith(eventId: currentWorkspace.id);
            await repository.insertPrepayment(prepaymentWithEventId);
            successCount++;
            LoggerUtils.logInfo('선입금 등록 성공: ${prepayment.buyerName} (eventId: ${currentWorkspace.id})', tag: 'ExcelImportScreen');
          } catch (e) {
            LoggerUtils.logError(
              '선입금 등록 중 오류 (주문번호: ${prepayment.buyerName})',
              error: e,
            );
          }
        }
        LoggerUtils.logInfo('선입금 등록 완료: $successCount개 성공', tag: 'ExcelImportScreen');

        // [복원] 선입금에 포함된 모든 가상상품을 이름 기준 중복 없이, 수량 누적하여 DB에 저장
        try {
          LoggerUtils.logInfo('가상상품 일괄 저장(누적) 시작', tag: 'ExcelImportScreen');
          // prepayment_virtual_product 테이블이 없으면 생성 (eventId 컬럼 포함)
          await db.execute('''
            CREATE TABLE IF NOT EXISTS prepayment_virtual_product (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL,
              price REAL NOT NULL DEFAULT 0,
              quantity INTEGER NOT NULL DEFAULT 0,
              createdAt TEXT NOT NULL,
              updatedAt TEXT,
              eventId INTEGER NOT NULL DEFAULT 1,
              FOREIGN KEY (eventId) REFERENCES events (id) ON DELETE CASCADE
            )
          ''');
          // 1. 모든 선입금 데이터에서 가상상품별 총수량 집계
          final Map<String, int> virtualProductQuantityMap = {};
          for (final data in _prepaymentData) {
            for (final entry in data.purchasedProducts.entries) {
              final vpName = entry.key.name.trim();
              if (vpName.isEmpty) continue;
              final vpNameLower = vpName.toLowerCase();
              virtualProductQuantityMap[vpNameLower] = (virtualProductQuantityMap[vpNameLower] ?? 0) + entry.value;
            }
          }
          // 2. DB에서 현재 워크스페이스의 기존 가상상품 목록 조회
          final allVirtualProducts = await db.query(
            'prepayment_virtual_product',
            where: 'eventId = ?',
            whereArgs: [currentWorkspace.id],
          );
          final Map<String, Map<String, dynamic>> existingVirtualProducts = {
            for (final e in allVirtualProducts) (e['name'] as String).trim().toLowerCase(): e
          };
          // 3. insert/update (수량 누적, createdAt/updatedAt 관리)
          for (final entry in virtualProductQuantityMap.entries) {
            final vpNameLower = entry.key;
            final quantityToAdd = entry.value;
            if (existingVirtualProducts.containsKey(vpNameLower)) {
              // 이미 존재: 수량 누적 update
              final existing = existingVirtualProducts[vpNameLower]!;
              final newQuantity = (existing['quantity'] as int) + quantityToAdd;
              await db.update(
                'prepayment_virtual_product',
                {
                  'quantity': newQuantity,
                  'updatedAt': DateTime.now().toIso8601String(),
                },
                where: 'name = ? AND eventId = ?',
                whereArgs: [existing['name'], currentWorkspace.id],
              );
              LoggerUtils.logInfo('가상상품 수량 누적 update: ${existing['name']} ($newQuantity개) - 행사: ${currentWorkspace.name}', tag: 'ExcelImportScreen');
            } else {
              // 신규: insert
              await db.insert('prepayment_virtual_product', {
                'name': vpNameLower, // 소문자 저장(중복 방지)
                'price': 0.0,
                'quantity': quantityToAdd,
                'createdAt': DateTime.now().toIso8601String(),
                'updatedAt': null,
                'eventId': currentWorkspace.id, // 현재 워크스페이스 ID 설정
              });
              LoggerUtils.logInfo('가상상품 신규 insert: $vpNameLower ($quantityToAdd개) - 행사: ${currentWorkspace.name}', tag: 'ExcelImportScreen');
            }
          }
          LoggerUtils.logInfo('가상상품 일괄 저장(누적) 완료', tag: 'ExcelImportScreen');
        } catch (e) {
          LoggerUtils.logError('가상상품 일괄 저장(누적) 중 오류', error: e, tag: 'ExcelImportScreen');
        }

        // 단순화된 상태 업데이트
        try {
          LoggerUtils.logInfo('선입금 목록 새로고침 시작', tag: 'ExcelImportScreen');
          await notifier.loadPrepayments(showLoading: false);
          LoggerUtils.logInfo('선입금 목록 새로고침 완료', tag: 'ExcelImportScreen');
        } catch (e) {
          LoggerUtils.logError(
            '선입금 목록 새로고침 중 오류',
            error: e,
          );
        }
        // 가상상품 Provider에 DB 최신 데이터 즉시 반영
        await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
      }
      
      // 재고 연동이 활성화된 경우 재고 차감
      if (linkInventory && successCount > 0) {
        LoggerUtils.logInfo('재고 연동 시작: $successCount개 선입금', tag: 'ExcelImportScreen');
        for (final data in _prepaymentData) {
          await _updateInventoryForPrepayment(data.purchasedProducts, productNotifier);
        }
        LoggerUtils.logInfo('재고 연동 완료', tag: 'ExcelImportScreen');
      }

      final message = linkInventory 
          ? '$successCount개의 선입금이 등록되었습니다. (재고 자동 차감됨)'
          : '$successCount개의 선입금이 등록되었습니다.';
      
      LoggerUtils.logInfo('선입금 등록 완료: $message', tag: 'ExcelImportScreen');
      // 정상 등록 시 토스트 메시지 제거
      
      // 등록 완료 후 데이터 초기화
      if (!mounted) return;
      setState(() {
        _prepaymentData = [];
        _selectedFileName = null;
      });
      
      // 컨트롤러들 정리
      for (final controller in _priceControllers.values) {
        controller.dispose();
      }
      _priceControllers.clear();
      // 등록 완료 안내 전체화면 페이지로 이동
      if (!mounted) return;
      await Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => RegistrationCompletePage(
            description: message,
          ),
        ),
      );
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '선입금 일괄 등록 중 오류',
        error: e,
        stackTrace: stackTrace,
      );
      if (!mounted) return;
      ToastUtils.showError(context, '등록 중 오류가 발생했습니다: $e');
    } finally {
      if (!mounted) return;
      setState(() {
        _isProcessing = false;
      });
    }
  }

  // [개선] 연동 플로우를 단일 함수로 통합
  Future<void> runLinkFlow(BuildContext context) async {
    LoggerUtils.logInfo('[runLinkFlow] 연동 안내 프롬프트 진입', tag: 'ExcelImportScreen');
    LoggerUtils.logDebug(
      '[runLinkFlow] _prepaymentData.length: ${_prepaymentData.length}',
      tag: 'ExcelImportScreen',
    );
    ToastUtils.showInfo(context, '[runLinkFlow] 연동 안내 프롬프트 진입');
    final linkResult = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => RegistrationLinkPromptPage(
          prepaymentData: _prepaymentData,
        ),
      ),
    );
    LoggerUtils.logDebug(
      '[runLinkFlow] linkResult: $linkResult',
      tag: 'ExcelImportScreen',
    );
    LoggerUtils.logInfo('[runLinkFlow] linkResult: $linkResult', tag: 'ExcelImportScreen');
    ToastUtils.showInfo(context, '[runLinkFlow] linkResult: $linkResult');
    if (linkResult != 'link') {
      LoggerUtils.logInfo('[runLinkFlow] 연동하지 않기 선택, 메인으로 이동', tag: 'ExcelImportScreen');
      LoggerUtils.logDebug(
        '[runLinkFlow] 연동하지 않기 선택, 메인으로 이동',
        tag: 'ExcelImportScreen',
      );
      ToastUtils.showInfo(context, '[runLinkFlow] 연동하지 않기 선택, 메인으로 이동');
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const InventoryScreen()),
        (route) => false,
      );
      return;
    }
    // 2. 실제 연동 로직 실행
    String? errorMsg;
    int linkedCount = 0;
    LoggerUtils.logDebug(
      '[runLinkFlow] 연동 로직 실행 시작, _prepaymentData.length: ${_prepaymentData.length}',
      tag: 'ExcelImportScreen',
    );
    LoggerUtils.logInfo('[runLinkFlow] 연동 로직 실행 시작, _prepaymentData.length:  ${_prepaymentData.length}', tag: 'ExcelImportScreen');
    ToastUtils.showInfo(context, '[runLinkFlow] 연동 로직 실행 시작, _prepaymentData.length:  ${_prepaymentData.length}');
    try {
      final databaseService = ref.read(databaseServiceProvider);
      final linkService = LinkService(databaseService: databaseService);
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        throw Exception('현재 행사가 선택되지 않았습니다');
      }

      final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
      if (currentEvent == null) {
        throw Exception('행사 정보를 불러올 수 없습니다');
      }

      linkedCount = await linkService.linkProductsWithPrepayments(
        prepaymentDataList: _prepaymentData,
        context: context,
        eventId: currentEvent.id ?? currentWorkspace.id,
      );
      LoggerUtils.logInfo('[runLinkFlow] 연동 성공, linkedCount=$linkedCount', tag: 'ExcelImportScreen');
      LoggerUtils.logDebug(
        '[runLinkFlow] 연동 성공, linkedCount=$linkedCount',
        tag: 'ExcelImportScreen',
      );
      // ToastUtils.showSuccess(context, '[runLinkFlow] 연동 성공, linkedCount=$linkedCount'); // 정상 연동 시 토스트 제거
    } catch (e) {
      errorMsg = e.toString();
      LoggerUtils.logError('[runLinkFlow] 연동 중 오류', error: e, tag: 'ExcelImportScreen');
      LoggerUtils.logError(
        '[runLinkFlow] 연동 중 오류',
        error: e,
        tag: 'ExcelImportScreen',
      );
      ToastUtils.showError(context, '[runLinkFlow] 연동 중 오류: $e');
    }
    // 3. 연동 후 필요한 Provider만 새로고침 (실시간 동기화로 인해 대부분 자동 갱신됨)
    try {
      // 연결 관련 데이터만 수동 새로고침
      await ref.read(prepaymentProductLinkNotifierProvider.notifier).loadLinks();
      await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
      LoggerUtils.logInfo('[runLinkFlow] 연결 데이터 동기화 완료', tag: 'ExcelImportScreen');
      LoggerUtils.logDebug(
        '[runLinkFlow] 연결 데이터 동기화 완료',
        tag: 'ExcelImportScreen',
      );
      ToastUtils.showInfo(context, '[runLinkFlow] 연결 데이터 동기화 완료');
    } catch (e) {
      LoggerUtils.logError('[runLinkFlow] 연결 데이터 동기화 중 오류', error: e, tag: 'ExcelImportScreen');
      LoggerUtils.logError(
        '[runLinkFlow] 연결 데이터 동기화 중 오류',
        error: e,
        tag: 'ExcelImportScreen',
      );
      ToastUtils.showError(context, '[runLinkFlow] 연결 데이터 동기화 중 오류: $e');
    }
    // 4. 결과 안내 페이지로 이동
    LoggerUtils.logDebug(
      '[runLinkFlow] RegistrationCompletePage로 이동',
      tag: 'ExcelImportScreen',
    );
    LoggerUtils.logInfo('[runLinkFlow] RegistrationCompletePage로 이동', tag: 'ExcelImportScreen');
    ToastUtils.showInfo(context, '[runLinkFlow] RegistrationCompletePage로 이동');
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RegistrationCompletePage(
          description: errorMsg == null
              ? '$linkedCount개의 연동 데이터가 생성되었습니다.'
              : '연동 중 오류가 발생했습니다: $errorMsg',
          onConfirm: () async {
            LoggerUtils.logDebug(
              '[RegistrationCompletePage] onConfirm 호출됨, 메인으로 이동',
              tag: 'ExcelImportScreen',
            );
            LoggerUtils.logInfo('[RegistrationCompletePage] onConfirm 호출됨, 메인으로 이동', tag: 'ExcelImportScreen');
            ToastUtils.showInfo(context, '[RegistrationCompletePage] onConfirm 호출됨, 메인으로 이동');
            try {
              await ref.read(productNotifierProvider.notifier).loadProducts();
              await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();
              await ref.read(prepaymentProductLinkNotifierProvider.notifier).loadLinks();
              await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
            } catch (_) {}
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const InventoryScreen()),
              (route) => false,
            );
          },
        ),
      ),
    );
  }





  /// 선입금에 대한 재고를 업데이트합니다.
  Future<void> _updateInventoryForPrepayment(
    Map<PrepaymentVirtualProduct, int> purchasedProducts,
    dynamic productNotifier,
  ) async {
    try {
      // 현재 상품 목록 가져오기
      final allProducts = productNotifier.state.products;
      
      for (final entry in purchasedProducts.entries) {
        final productName = entry.key.name; // 가상 상품의 이름 사용
        final quantity = entry.value;
        
        // 해당 상품 찾기
        final productsWithSameName = allProducts.where((p) => p.name == productName).toList();
        final product = productsWithSameName.isNotEmpty ? productsWithSameName.first : null;
        
        if (product != null) {
          // 재고 차감 - decreaseStock 메서드 사용으로 중복 Firebase 업로드 방지
          final productRepo = ref.read(productRepositoryProvider);
          try {
            await productRepo.decreaseStock(product.id!, quantity);
          } catch (e) {
            LoggerUtils.logWarning(
              '재고 부족: ${product.name} (현재: ${product.quantity}, 필요: $quantity)',
              tag: 'ExcelImportScreen',
            );
          }
        }
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '재고 업데이트 중 오류',
        error: e,
        stackTrace: stackTrace,
        tag: 'ExcelImportScreen',
      );
    }
  }





  /// 선입금 상세 정보를 표시합니다.
  void _showPrepaymentDetail(ExcelPrepaymentData data) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('주문번호: ${data.orderNumber}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('구매자: ${data.buyerName}'),
              Text('연락처: ${data.contact}'),
              Text('이메일: ${data.email}'),
              Text('은행: ${data.bankName}'),
              if (data.twitterAccount != null && data.twitterAccount!.isNotEmpty)
                Text('트위터: ${data.twitterAccount}'),
              Text('요일: ${data.dayOfWeek ?? '없음'}'),
              if (data.memo != null && data.memo!.isNotEmpty) ...[
                const SizedBox(height: 8),
                const Text('메모:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                ...data.memo!.split('\n').map((item) => Text(item)),
              ],
              const SizedBox(height: 16),
              const Text('구매 상품:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...data.purchasedProducts.entries.map((entry) {
                return Text('  • ${entry.key.name}: ${entry.value}개'); // 가상 상품의 이름 사용
              }),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('닫기'),
          ),
        ],
      ),
    );
  }
} 
