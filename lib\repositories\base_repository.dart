import 'dart:async';
import 'package:sqflite/sqflite.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';
import '../utils/common_utils.dart';

/// 모든 Repository의 베이스 클래스
/// 
/// 공통 데이터베이스 작업과 유틸리티 메서드를 제공합니다.
abstract class BaseRepository {
  final DatabaseService _databaseService;
  final String _tag;

  BaseRepository(this._databaseService, this._tag);

  /// 데이터베이스 인스턴스 getter
  Future<Database> get database => _databaseService.database;

  /// 안전한 쿼리 실행
  Future<List<Map<String, dynamic>>> safeQuery(
    String table, {
    bool? distinct,
    List<String>? columns,
    String? where,
    List<Object?>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await database;
      return await db.query(
        table,
        distinct: distinct,
        columns: columns,
        where: where,
        whereArgs: whereArgs,
        groupBy: groupBy,
        having: having,
        orderBy: orderBy,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      LoggerUtils.logError('Query failed on table: $table', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 안전한 삽입
  Future<int> safeInsert(
    String table,
    Map<String, Object?> values, {
    String? nullColumnHack,
    ConflictAlgorithm? conflictAlgorithm,
  }) async {
    try {
      final db = await database;
      return await db.insert(
        table,
        values,
        nullColumnHack: nullColumnHack,
        conflictAlgorithm: conflictAlgorithm,
      );
    } catch (e) {
      LoggerUtils.logError('Insert failed on table: $table', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 안전한 업데이트
  Future<int> safeUpdate(
    String table,
    Map<String, Object?> values, {
    String? where,
    List<Object?>? whereArgs,
    ConflictAlgorithm? conflictAlgorithm,
  }) async {
    try {
      final db = await database;
      return await db.update(
        table,
        values,
        where: where,
        whereArgs: whereArgs,
        conflictAlgorithm: conflictAlgorithm,
      );
    } catch (e) {
      LoggerUtils.logError('Update failed on table: $table', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 안전한 삭제
  Future<int> safeDelete(
    String table, {
    String? where,
    List<Object?>? whereArgs,
  }) async {
    try {
      final db = await database;
      return await db.delete(
        table,
        where: where,
        whereArgs: whereArgs,
      );
    } catch (e) {
      LoggerUtils.logError('Delete failed on table: $table', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 안전한 Raw 쿼리
  Future<List<Map<String, dynamic>>> safeRawQuery(
    String sql, [
    List<Object?>? arguments,
  ]) async {
    try {
      final db = await database;
      return await db.rawQuery(sql, arguments);
    } catch (e) {
      LoggerUtils.logError('Raw query failed: $sql', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 안전한 Raw 삽입
  Future<int> safeRawInsert(
    String sql, [
    List<Object?>? arguments,
  ]) async {
    try {
      final db = await database;
      return await db.rawInsert(sql, arguments);
    } catch (e) {
      LoggerUtils.logError('Raw insert failed: $sql', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 안전한 Raw 업데이트
  Future<int> safeRawUpdate(
    String sql, [
    List<Object?>? arguments,
  ]) async {
    try {
      final db = await database;
      return await db.rawUpdate(sql, arguments);
    } catch (e) {
      LoggerUtils.logError('Raw update failed: $sql', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 안전한 Raw 삭제
  Future<int> safeRawDelete(
    String sql, [
    List<Object?>? arguments,
  ]) async {
    try {
      final db = await database;
      return await db.rawDelete(sql, arguments);
    } catch (e) {
      LoggerUtils.logError('Raw delete failed: $sql', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 안전한 트랜잭션 실행
  Future<T> safeTransaction<T>(
    Future<T> Function(Transaction txn) action, {
    String? taskName,
  }) async {
    final dbService = _databaseService;
    if (dbService is DatabaseServiceImpl) {
      return await dbService.safeTransaction(
        action,
        taskName: taskName ?? _tag,
      );
    } else {
      // Fallback for other implementations
      final db = await database;
      return await db.transaction(action);
    }
  }

  /// 배치 삽입
  Future<void> batchInsert(
    String table,
    List<Map<String, Object?>> values, {
    int batchSize = 100,
    ConflictAlgorithm? conflictAlgorithm,
  }) async {
    if (values.isEmpty) return;

    try {
      await safeTransaction<void>((txn) async {
        for (int i = 0; i < values.length; i += batchSize) {
          final batch = values.skip(i).take(batchSize);
          for (final value in batch) {
            await txn.insert(
              table,
              value,
              conflictAlgorithm: conflictAlgorithm,
            );
          }
        }
      }, taskName: 'batchInsert_$table');

      LoggerUtils.logInfo(
        'Batch insert completed: ${values.length} records in $table',
        tag: _tag,
      );
    } catch (e) {
      LoggerUtils.logError('Batch insert failed on table: $table', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 배치 업데이트
  Future<void> batchUpdate(
    String table,
    List<Map<String, Object?>> updates,
    String whereColumn, {
    int batchSize = 100,
  }) async {
    if (updates.isEmpty) return;

    try {
      await safeTransaction<void>((txn) async {
        for (int i = 0; i < updates.length; i += batchSize) {
          final batch = updates.skip(i).take(batchSize);
          for (final update in batch) {
            final whereValue = update[whereColumn];
            final updateData = Map<String, Object?>.from(update);
            updateData.remove(whereColumn);

            await txn.update(
              table,
              updateData,
              where: '$whereColumn = ?',
              whereArgs: [whereValue],
            );
          }
        }
      }, taskName: 'batchUpdate_$table');

      LoggerUtils.logInfo(
        'Batch update completed: ${updates.length} records in $table',
        tag: _tag,
      );
    } catch (e) {
      LoggerUtils.logError('Batch update failed on table: $table', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 존재 여부 확인
  Future<bool> exists(
    String table, {
    String? where,
    List<Object?>? whereArgs,
  }) async {
    try {
      final result = await safeQuery(
        table,
        columns: ['COUNT(*) as count'],
        where: where,
        whereArgs: whereArgs,
        limit: 1,
      );

      if (result.isNotEmpty) {
        final count = CommonUtils.safeParseInt(result.first['count']);
        return count > 0;
      }

      return false;
    } catch (e) {
      LoggerUtils.logError('Exists check failed on table: $table', tag: _tag, error: e);
      return false;
    }
  }

  /// 레코드 수 조회
  Future<int> count(
    String table, {
    String? where,
    List<Object?>? whereArgs,
  }) async {
    try {
      final result = await safeQuery(
        table,
        columns: ['COUNT(*) as count'],
        where: where,
        whereArgs: whereArgs,
        limit: 1,
      );

      if (result.isNotEmpty) {
        return CommonUtils.safeParseInt(result.first['count']);
      }

      return 0;
    } catch (e) {
      LoggerUtils.logError('Count failed on table: $table', tag: _tag, error: e);
      return 0;
    }
  }

  /// 페이지네이션 지원 쿼리
  Future<List<Map<String, dynamic>>> paginatedQuery(
    String table, {
    List<String>? columns,
    String? where,
    List<Object?>? whereArgs,
    String? orderBy,
    int page = 1,
    int pageSize = 50,
  }) async {
    final offset = (page - 1) * pageSize;
    
    return await safeQuery(
      table,
      columns: columns,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: pageSize,
      offset: offset,
    );
  }
}
