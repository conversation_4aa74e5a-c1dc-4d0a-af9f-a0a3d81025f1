// Firebase Firestore 보안 규칙 - App Check 호환 버전

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 사용자 인증 확인 함수
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // 사용자 소유권 확인 함수
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    // App Check 토큰 확인 (임시 비활성화)
    function isAppCheckValid() {
      // 개발 중에는 App Check 검증을 완전히 우회
      return true;
    }
    
    // 사용자 문서에 대한 규칙
    match /users/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId) && isAppCheckValid();
      
      // 이벤트 하위 컬렉션
      match /events/{eventId} {
        allow read, write: if isAuthenticated() && isOwner(userId) && isAppCheckValid();
        
        // 상품 하위 컬렉션
        match /products/{productId} {
          allow read, write: if isAuthenticated() && isOwner(userId) && isAppCheckValid();
        }
        
        // 가상 상품 하위 컬렉션 (누락된 규칙 추가)
        match /virtual_products/{productId} {
          allow read, write: if isAuthenticated() && isOwner(userId) && isAppCheckValid();
        }
        
        // 상품 링크 하위 컬렉션 (누락된 규칙 추가)
        match /product_links/{linkId} {
          allow read, write: if isAuthenticated() && isOwner(userId) && isAppCheckValid();
        }
        
        // 판매자 하위 컬렉션
        match /sellers/{sellerId} {
          allow read, write: if isAuthenticated() && isOwner(userId) && isAppCheckValid();
        }
        
        // 선입금 하위 컬렉션
        match /prepayments/{prepaymentId} {
          allow read, write: if isAuthenticated() && isOwner(userId) && isAppCheckValid();
        }
        
        // 판매 로그 하위 컬렉션
        match /sales_logs/{logId} {
          allow read, write: if isAuthenticated() && isOwner(userId) && isAppCheckValid();
        }
        
        // 할인 설정 하위 컬렉션
        match /set_discounts/{discountId} {
          allow read, write: if isAuthenticated() && isOwner(userId) && isAppCheckValid();
        }
      }
    }
  }
}
