import '../models/product.dart';

/// 상품 검색 로직을 담당하는 클래스
///
/// 주요 기능:
/// - 상품 검색
/// - 검색어 정규화
/// - 다중 필드 검색 (상품명, 판매자명, 가격, 수량)
/// - 실시간 상태 관리
class ProductSearchLogic {
  /// 상품 검색 기능
  ///
  /// [query]: 검색어
  /// [onSearchComplete]: 검색 완료 시 호출될 콜백
  /// [allProducts]: 전체 상품 목록
  static void searchProducts(
    String query,
    void Function(List<Product>) onSearchComplete,
    List<Product> allProducts,
  ) {
    _performProductSearch(query, onSearchComplete, allProducts);
  }

  /// 실제 상품 검색 수행
  ///
  /// [query]: 검색어
  /// [onSearchComplete]: 검색 완료 시 호출될 콜백
  /// [allProducts]: 전체 상품 목록
  static Future<void> _performProductSearch(
    String query,
    void Function(List<Product>) onSearchComplete,
    List<Product> allProducts,
  ) async {
    try {
      final normalizedQuery = normalizeSearchQuery(query);

      if (normalizedQuery.isEmpty) {
        // 빈 검색어 시 전체 데이터 반환
        onSearchComplete(List.from(allProducts));
        return;
      }

      final searchResults = allProducts
          .where((product) => _matchesProductSearchQuery(product, normalizedQuery))
          .toList();

      onSearchComplete(searchResults);
    } catch (e) {
      // 에러 발생 시 빈 리스트 반환
      onSearchComplete([]);
    }
  }

  /// 상품이 검색어와 매치되는지 확인
  ///
  /// [product]: 확인할 상품
  /// [normalizedQuery]: 정규화된 검색어
  /// 반환값: 매치 여부
  static bool _matchesProductSearchQuery(Product product, String normalizedQuery) {
    // 상품명으로 검색
    if (_optimizedTextMatch(product.name, normalizedQuery)) {
      return true;
    }

    // 판매자명으로 검색
    if (product.sellerName != null && _optimizedTextMatch(product.sellerName!, normalizedQuery)) {
      return true;
    }

    // 가격으로 검색 (숫자 문자열 매칭)
    if (_optimizedTextMatch(product.price.toString(), normalizedQuery)) {
      return true;
    }

    // 수량으로 검색 (숫자 문자열 매칭)
    if (_optimizedTextMatch(product.quantity.toString(), normalizedQuery)) {
      return true;
    }

    return false;
  }

  /// 텍스트 매칭 최적화 함수
  ///
  /// [text]: 검색 대상 텍스트
  /// [query]: 검색어
  /// 반환값: 매치 여부
  static bool _optimizedTextMatch(String text, String query) {
    final normalizedText = text.toLowerCase();
    final normalizedQuery = query.toLowerCase();
    return normalizedText.contains(normalizedQuery);
  }

  /// 즉시 검색 수행
  ///
  /// [query]: 검색어
  /// [allProducts]: 전체 상품 목록
  /// 반환값: 검색 결과
  static List<Product> searchProductsImmediate(
    String query,
    List<Product> allProducts,
  ) {
    if (query.isEmpty) return List.from(allProducts);

    final normalizedQuery = normalizeSearchQuery(query);
    
    return allProducts.where(
      (product) => _matchesProductSearchQuery(product, normalizedQuery),
    ).toList();
  }

  /// 검색어 유효성 검사
  ///
  /// [query]: 검색어
  /// 반환값: 유효성 여부
  static bool isValidSearchQuery(String query) {
    if (query.isEmpty) return true;
    
    // 최소 길이 검사
    if (query.trim().isEmpty) return false;
    
    // 최대 길이 검사
    if (query.length > 100) return false;
    
    // 특수 문자 검사 (기본적인 검색어는 허용)
    final invalidPattern = RegExp(r'[<>{}()\[\]]');
    if (invalidPattern.hasMatch(query)) return false;
    
    return true;
  }

  /// 검색어 정규화
  ///
  /// [query]: 원본 검색어
  /// 반환값: 정규화된 검색어
  static String normalizeSearchQuery(String query) {
    if (query.isEmpty) return '';
    
    // 공백 정리
    var normalized = query.trim().replaceAll(RegExp(r'\s+'), ' ');
    
    // 소문자 변환
    normalized = normalized.toLowerCase();
    
    return normalized;
  }

  /// 검색 결과 하이라이트를 위한 키워드 추출
  ///
  /// [query]: 검색어
  /// 반환값: 하이라이트할 키워드 목록
  static List<String> extractHighlightKeywords(String query) {
    final normalizedQuery = normalizeSearchQuery(query);
    if (normalizedQuery.isEmpty) return [];
    
    // 공백으로 분리하여 키워드 추출
    return normalizedQuery
        .split(' ')
        .where((keyword) => keyword.isNotEmpty)
        .toList();
  }

  /// 검색 결과 순위 계산
  ///
  /// [product]: 상품
  /// [query]: 검색어
  /// 반환값: 순위 점수 (높을수록 우선순위 높음)
  static int calculateSearchRank(Product product, String query) {
    final normalizedQuery = normalizeSearchQuery(query);
    if (normalizedQuery.isEmpty) return 0;
    
    int rank = 0;
    
    // 상품명 매치 (가장 높은 우선순위)
    if (_optimizedTextMatch(product.name, normalizedQuery)) {
      rank += 100;
      
      // 정확한 매치인 경우 추가 점수
      if (product.name.toLowerCase().contains(normalizedQuery.toLowerCase())) {
        rank += 50;
      }
    }
    
    // 판매자명 매치
    if (product.sellerName != null && _optimizedTextMatch(product.sellerName!, normalizedQuery)) {
      rank += 80;
    }
    
    // 가격 매치
    if (_optimizedTextMatch(product.price.toString(), normalizedQuery)) {
      rank += 30;
    }
    
    // 수량 매치
    if (_optimizedTextMatch(product.quantity.toString(), normalizedQuery)) {
      rank += 20;
    }
    
    return rank;
  }
} 