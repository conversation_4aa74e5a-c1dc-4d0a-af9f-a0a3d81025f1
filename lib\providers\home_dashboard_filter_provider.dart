import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/sales_log.dart';
import '../models/prepayment.dart';
import '../providers/sales_log_provider.dart';
import '../providers/prepayment_provider.dart';

/// 홈 대시보드 필터링 상태를 관리하는 Provider들

/// 홈 대시보드 날짜 범위 필터 Provider
final homeDashboardDateRangeProvider = StateProvider<DateTimeRange?>((ref) {
  return null; // null이면 전체 기간
});

/// 홈 대시보드 판매자 필터 Provider
final homeDashboardSellerFilterProvider = StateProvider<String>((ref) {
  return '전체'; // 기본값은 '전체'
});

/// 선입금 요일 필터 Provider (홈 대시보드용)
final homeDashboardPrepaymentDayFilterProvider = StateProvider<int>((ref) {
  return 0; // 0: 전체, 1-7: 요일, 8: 없음
});

/// 홈 대시보드에서 필터링된 판매 로그를 제공하는 Provider
final homeDashboardFilteredSalesLogsProvider = Provider<List<SalesLog>>((ref) {
  final salesLogState = ref.watch(salesLogNotifierProvider);
  final dateRange = ref.watch(homeDashboardDateRangeProvider);
  final selectedSeller = ref.watch(homeDashboardSellerFilterProvider);
  
  final allSalesLogs = salesLogState.salesLogs;
  
  // 필터링 적용
  List<SalesLog> filtered = allSalesLogs.where((log) {
    // 판매자 필터
    if (selectedSeller != '전체') {
      if ((log.sellerName ?? '알 수 없음') != selectedSeller) {
        return false;
      }
    }
    
    // 날짜 범위 필터
    if (dateRange != null) {
      final logDate = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
      final startDate = dateRange.start;
      final endDate = dateRange.end.add(const Duration(days: 1)); // 종료일 포함
      
      if (logDate.isBefore(startDate) || logDate.isAfter(endDate)) {
        return false;
      }
    }
    
    return true;
  }).toList();
  
  return filtered;
});

/// 홈 대시보드에서 필터링된 선입금을 제공하는 Provider
final homeDashboardFilteredPrepaymentsProvider = Provider<List<Prepayment>>((ref) {
  final prepaymentState = ref.watch(prepaymentNotifierProvider);
  final selectedDay = ref.watch(homeDashboardPrepaymentDayFilterProvider);
  
  final allPrepayments = prepaymentState.prepayments;
  
  // 요일 필터링 적용
  if (selectedDay == 0) {
    return allPrepayments; // 전체
  }
  
  List<Prepayment> filtered = allPrepayments.where((p) {
    bool match = false;
    if (selectedDay == 8) {
      // '없음' 처리
      if (p.registrationActualDayOfWeek == 0 || p.registrationActualDayOfWeek == 8) {
        match = true;
      }
      if (p.pickupDays.any((d) => d == Prepayment.noDayOfWeek || 
          Prepayment.availableDaysOfWeek.indexOf(d) == 7)) {
        match = true;
      }
    } else {
      if (p.registrationActualDayOfWeek == selectedDay) {
        match = true;
      }
      if (p.pickupDays.any((d) => 
          Prepayment.availableDaysOfWeek.indexOf(d) == (selectedDay - 1))) {
        match = true;
      }
    }
    return match;
  }).toList();
  
  return filtered;
});

/// 홈 대시보드에서 사용할 수 있는 요일 목록을 제공하는 Provider
final homeDashboardAvailableDaysProvider = Provider<Set<int>>((ref) {
  final prepayments = ref.watch(prepaymentNotifierProvider).prepayments;
  final Set<int> days = {};
  
  for (final p in prepayments) {
    // registrationActualDayOfWeek가 1~7, 8('없음')이면 추가
    if (p.registrationActualDayOfWeek >= 1 && p.registrationActualDayOfWeek <= 7) {
      days.add(p.registrationActualDayOfWeek);
    } else if (p.registrationActualDayOfWeek == 0 || p.registrationActualDayOfWeek == 8) {
      days.add(8); // '없음'
    }
    
    // pickupDays에도 요일명이 있으면 추가
    for (final dayName in p.pickupDays) {
      final idx = Prepayment.availableDaysOfWeek.indexOf(dayName);
      if (idx >= 0 && idx < 7) {
        days.add(idx + 1); // 1~7
      } else if (dayName == Prepayment.noDayOfWeek || idx == 7) {
        days.add(8); // '없음'
      }
    }
  }
  
  return days;
});

/// 필터링된 기간의 판매 통계를 계산하는 Provider
final homeDashboardSalesStatsProvider = Provider<Map<String, int>>((ref) {
  final filteredSalesLogs = ref.watch(homeDashboardFilteredSalesLogsProvider);
  
  int totalSales = 0;
  int salesCount = 0;
  int productCount = 0;
  int serviceCount = 0;
  
  for (final log in filteredSalesLogs) {
    totalSales += log.totalAmount;
    salesCount++;
    
    if (log.transactionType.name == 'sale') {
      productCount += log.soldQuantity;
    } else if (log.transactionType.name == 'service') {
      serviceCount += log.soldQuantity;
    }
  }
  
  return {
    'totalSales': totalSales,
    'salesCount': salesCount,
    'productCount': productCount,
    'serviceCount': serviceCount,
  };
});
