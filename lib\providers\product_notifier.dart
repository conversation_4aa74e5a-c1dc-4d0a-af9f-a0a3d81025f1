import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../models/product.dart';
import '../models/product_sort_option.dart';
import '../models/sales_log.dart';
import '../repositories/product_repository.dart';
import '../repositories/sales_log_repository.dart';
import '../utils/logger_utils.dart';

import 'product_state.dart';
import '../models/event_workspace.dart';
import 'unified_workspace_provider.dart';
import 'realtime_sync_provider.dart';
import '../services/realtime_sync_service_main.dart';
import '../services/database_service.dart';
import 'data_sync_provider.dart';

/// 상품 데이터베이스 접근을 위한 Repository Provider는 product_provider.dart에서 정의됨

class ProductNotifier extends StateNotifier<ProductState> {
  final ProductRepository repository;
  final Ref ref;
  final bool autoInit;
  String _searchQuery = '';
  Timer? _debounceTimer;
  bool _isPaused = false;
  StreamSubscription<RealtimeDataChange>? _realtimeSubscription;
  StreamSubscription<QuerySnapshot>? _firestoreSubscription; // 직접 Firestore 구독용
  final Set<int> _recentlyAddedProducts = {}; // 최근 추가한 상품 ID 캐시 (무한 루프 방지)
  final Set<int> _recentlyDeletedProducts = {}; // 최근 삭제한 상품 ID 캐시 (무한 루프 방지)

  ProductNotifier(this.repository, this.ref, {this.autoInit = true}) : super(ProductState.initialState()) {
    // 즉시 로딩하지 않고 워크스페이스가 설정된 후에만 로딩
    _watchCurrentEvent();
    _setupDirectFirestoreSync(); // 새로운 직접 실시간 동기화
    
    // autoInit이 true이고 currentWorkspace가 이미 설정되어 있는 경우에만 로딩
    if (autoInit) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final currentWorkspace = ref.read(currentWorkspaceProvider);
        if (currentWorkspace != null) {
          loadProducts(showLoading: false);
        }
      });
    }
  }

  /// 현재 행사 워크스페이스 변경 감지 및 자동 새로고침
  void _watchCurrentEvent() {
    ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) async {
      if (previous?.id != next?.id) {
        LoggerUtils.logInfo('현재 행사 워크스페이스 변경 감지 - ProductNotifier 새로고침: ${previous?.name} -> ${next?.name}', tag: 'ProductNotifier');
        
        // 행사 전환 시 메모리 완전 클리어
        _clearAllDataForEventTransition();
        
        // 실시간 동기화 서비스의 이벤트별 캐시도 정리
        if (previous?.id != null) {
          try {
            final realtimeService = ref.read(realtimeSyncServiceProvider);
            await realtimeService.clearEventMemoryCache(previous!.id);
          } catch (e) {
            LoggerUtils.logError('실시간 동기화 캐시 정리 실패: $e', tag: 'ProductNotifier');
          }
        }
        
        if (next != null) {
          loadProducts(showLoading: false);
          // _setupRealtimeSync(); // 기존 방식 비활성화
          _setupDirectFirestoreSync(); // 새 워크스페이스에 대한 직접 Firestore 동기화 재설정
        } else {
          // 현재 행사 워크스페이스가 null이 되면 상품 목록 클리어
          state = state.copyWith(
            products: [],
            filteredProducts: [],
            errorMessage: '행사 워크스페이스를 선택해주세요. 왼쪽 상단 메뉴에서 행사 워크스페이스를 선택하거나 생성할 수 있습니다.',
          );
        }
      }
    });
  }

  /// 실시간 동기화 설정 (현재 비활성화)
  /// 실시간 동기화 설정 (현재 비활성화)
  /*
  void _setupRealtimeSync() {
    try {
      // 기존 구독 해제
      _realtimeSubscription?.cancel();
      
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 워크스페이스가 없어 실시간 동기화를 설정할 수 없습니다', tag: 'ProductNotifier');
        return;
      }
      
      // 실시간 동기화 서비스 가져오기
      final realtimeService = ref.read(realtimeSyncServiceProvider);
      
      // 중복 구독 방지: 이미 다른 곳에서 구독이 시작되었다고 가정
      LoggerUtils.logInfo('실시간 데이터 스트림 구독 시작 (중복 구독 방지)', tag: 'ProductNotifier');
      
      // 데이터 변경 리스너 설정
      _realtimeSubscription = realtimeService.dataChanges.listen((change) {
        LoggerUtils.logInfo('ProductNotifier: 실시간 데이터 변경 수신됨', tag: 'ProductNotifier');
        _handleRealtimeDataChange(change);
      }, onError: (error) {
        LoggerUtils.logError('ProductNotifier: 실시간 데이터 변경 리스너 에러', tag: 'ProductNotifier', error: error);
      });
      
      LoggerUtils.logInfo('ProductNotifier 실시간 동기화 리스너 설정 완료', tag: 'ProductNotifier');
    } catch (e) {
      LoggerUtils.logError('ProductNotifier 실시간 동기화 설정 실패', tag: 'ProductNotifier', error: e);
    }
  }
  */

  /// 실시간 데이터 변경 처리 (현재 비활성화)
  /*
  void _handleRealtimeDataChange(RealtimeDataChange change) {
    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      
      LoggerUtils.logInfo('실시간 변경 감지됨: ${change.collectionName}, ${change.changeType.name}, documentId: ${change.documentId}, eventId: ${change.eventId}, 현재 워크스페이스: ${currentWorkspace?.id}', tag: 'ProductNotifier');
      
      // 현재 워크스페이스의 상품 변경인지 확인
      if (currentWorkspace?.id == change.eventId && change.collectionName == 'products') {
        LoggerUtils.logInfo('상품 실시간 변경 감지: ${change.changeType.name} - ${change.documentId}', tag: 'ProductNotifier');
        
        // 상품 목록 즉시 새로고침 (로딩 없이)
        loadProducts(showLoading: false);
      } else {
        LoggerUtils.logInfo('다른 워크스페이스 또는 다른 컬렉션의 변경: 무시됨', tag: 'ProductNotifier');
      }
    } catch (e) {
      LoggerUtils.logError('실시간 데이터 변경 처리 실패', tag: 'ProductNotifier', error: e);
    }
  }
  */

  /// 행사 전환 시 메모리 완전 클리어 (메모리 누수 방지)
  void _clearAllDataForEventTransition() {
    try {
      LoggerUtils.logInfo('행사 전환 - 메모리 클리어 시작', tag: 'ProductNotifier');

      // 1. 모든 구독 안전하게 해제
      try {
        _realtimeSubscription?.cancel();
        _realtimeSubscription = null;
      } catch (e) {
        LoggerUtils.logError('실시간 구독 해제 실패', tag: 'ProductNotifier', error: e);
      }

      try {
        _firestoreSubscription?.cancel();
        _firestoreSubscription = null;
      } catch (e) {
        LoggerUtils.logError('Firestore 구독 해제 실패', tag: 'ProductNotifier', error: e);
      }

      try {
        _debounceTimer?.cancel();
        _debounceTimer = null;
      } catch (e) {
        LoggerUtils.logError('디바운스 타이머 해제 실패', tag: 'ProductNotifier', error: e);
      }

      // 2. 상태 완전 초기화
      state = ProductState.initialState();

      // 3. 내부 변수 클리어
      _searchQuery = '';
      _recentlyAddedProducts.clear();

      LoggerUtils.logInfo('행사 전환 - 메모리 클리어 완료', tag: 'ProductNotifier');
    } catch (e) {
      LoggerUtils.logError('메모리 클리어 중 오류', tag: 'ProductNotifier', error: e);
    }
  }

  /// 직접 Firestore 실시간 동기화 설정 (Local-First 방식)
  void _setupDirectFirestoreSync() {
    try {
      // 기존 구독 해제
      _firestoreSubscription?.cancel();
      
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      final user = FirebaseAuth.instance.currentUser;
      
      if (currentWorkspace == null || user == null) {
        LoggerUtils.logWarning('워크스페이스 또는 사용자가 없어 직접 Firestore 동기화를 설정할 수 없습니다', tag: 'ProductNotifier');
        return;
      }
      
      LoggerUtils.logInfo('직접 Firestore 실시간 동기화 시작: eventId ${currentWorkspace.id}', tag: 'ProductNotifier');
      
      // Firestore 컬렉션 직접 구독
      _firestoreSubscription = FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(currentWorkspace.id.toString())
          .collection('products')
          .snapshots()
          .listen(
        (snapshot) {
          _handleFirestoreChanges(snapshot);
        },
        onError: (error) {
          LoggerUtils.logError('직접 Firestore 구독 에러', tag: 'ProductNotifier', error: error);
        },
      );
      
      LoggerUtils.logInfo('직접 Firestore 실시간 동기화 설정 완료', tag: 'ProductNotifier');
    } catch (e) {
      LoggerUtils.logError('직접 Firestore 실시간 동기화 설정 실패', tag: 'ProductNotifier', error: e);
    }
  }

  /// Firestore 변경사항을 Local-First 방식으로 처리 (무한 루프 방지)
  void _handleFirestoreChanges(QuerySnapshot snapshot) async {
    try {
      final docChanges = snapshot.docChanges;
      if (docChanges.isEmpty) return;
      
      LoggerUtils.logDebug('Firestore 변경사항 감지: ${docChanges.length}개 변경', tag: 'ProductNotifier');
      
      bool hasExternalChanges = false;
      
      for (final change in docChanges) {
        final docId = change.doc.id;
        final productId = int.tryParse(docId);
        
        if (productId == null) {
          LoggerUtils.logWarning('잘못된 상품 ID: $docId', tag: 'ProductNotifier');
          continue;
        }
        
        // 자기가 최근에 추가/삭제한 상품은 무시 (무한 루프 방지)
        if (_recentlyAddedProducts.contains(productId)) {
          LoggerUtils.logDebug('최근 추가한 상품 무시: ID $productId', tag: 'ProductNotifier');
          continue;
        }
        if (_recentlyDeletedProducts.contains(productId)) {
          LoggerUtils.logDebug('최근 삭제한 상품 무시: ID $productId', tag: 'ProductNotifier');
          continue;
        }
        
        switch (change.type) {
          case DocumentChangeType.added:
          case DocumentChangeType.modified:
            try {
              final data = change.doc.data() as Map<String, dynamic>;
              data['id'] = productId;
              final product = Product.fromJson(data);
              
              // 안전한 동기 처리로 즉시 로컬에 저장
              await _syncProductToLocalSafe(product);
              hasExternalChanges = true;
              
              LoggerUtils.logDebug('외부 상품 변경 처리 완료: ${product.name} (${change.type.name})', tag: 'ProductNotifier');
            } catch (e) {
              LoggerUtils.logError('상품 로컬 동기화 실패: ID $productId', tag: 'ProductNotifier', error: e);
            }
            break;
            
          case DocumentChangeType.removed:
            try {
              // 로컬 DB에서 실제 상품 정보 조회
              final existingProduct = await repository.getProductById(productId);
              if (existingProduct != null) {
                await repository.deleteProduct(existingProduct);
                hasExternalChanges = true;
                LoggerUtils.logDebug('외부 상품 삭제 완료: ID $productId, Name: ${existingProduct.name}', tag: 'ProductNotifier');
              } else {
                LoggerUtils.logDebug('외부 삭제 요청된 상품이 로컬에 존재하지 않음: ID $productId', tag: 'ProductNotifier');
              }
            } catch (e) {
              LoggerUtils.logError('상품 로컬 삭제 실패: ID $productId', tag: 'ProductNotifier', error: e);
            }
            break;
        }
      }
      
      // 외부 변경사항이 있을 때만 즉시 상태 업데이트
      if (hasExternalChanges && mounted) {
        LoggerUtils.logDebug('외부 변경사항으로 인한 즉시 상태 업데이트', tag: 'ProductNotifier');
        await loadProducts(showLoading: false);
      }
    } catch (e) {
      LoggerUtils.logError('Firestore 변경사항 처리 실패', tag: 'ProductNotifier', error: e);
    }
  }

  /// 상품을 안전하게 로컬 DB에 동기화 (크래시 방지)
  Future<void> _syncProductToLocalSafe(Product product) async {
    try {
      // 이미지는 네트워크 URL 그대로 저장 (빠른 동기화)
      final existingProducts = await repository.getProductsByEventId(product.eventId);
      final existingProduct = existingProducts.where((p) => p.id == product.id).firstOrNull;
      
      if (existingProduct != null) {
        await repository.updateProduct(product, updateRelatedSalesLogs: false);
      } else {
        await repository.insertProduct(product);
      }
      
      LoggerUtils.logDebug('상품 안전 동기화 완료: ${product.name}', tag: 'ProductNotifier');
    } catch (e) {
      LoggerUtils.logError('상품 안전 동기화 실패: ${product.name}', tag: 'ProductNotifier', error: e);
      rethrow;
    }
  }

  void pause() {
    if (!_isPaused) {
      _isPaused = true;
      LoggerUtils.logDebug('ProductNotifier paused', tag: 'ProductNotifier');
    }
  }

  void resume([Duration? delay]) {
    if (_isPaused) {
      _isPaused = false;
      LoggerUtils.logDebug('ProductNotifier resumed', tag: 'ProductNotifier');
      loadProducts(showLoading: false);
    }
  }

  /// 상품 목록을 로드합니다. (서버 fallback 포함)
  Future<void> loadProducts({bool showLoading = true}) async {
    LoggerUtils.methodStart('loadProducts', tag: 'ProductNotifier');
    if (showLoading) {
      state = state.copyWith(isLoading: true);
    }

    try {
      // 현재 선택된 행사 워크스페이스 확인
      EventWorkspace? currentWorkspace = ref.read(currentWorkspaceProvider);

      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 행사 워크스페이스가 없습니다', tag: 'ProductNotifier');
        state = state.copyWith(
          products: [],
          filteredProducts: [],
          isLoading: false,
          errorMessage: '행사 워크스페이스를 선택해주세요. 왼쪽 상단 메뉴에서 행사 워크스페이스를 선택하거나 생성할 수 있습니다.',
        );
        return;
      }

      // 성능 최적화: 병렬 처리 및 캐싱
      final stopwatch = Stopwatch()..start();

      // 1단계: 로컬 DB에서 상품 로드
      var products = await repository.getProductsByEventId(currentWorkspace.id);

      // 2단계: 로컬에 데이터가 없으면 서버에서 가져오기
      if (products.isEmpty) {
        LoggerUtils.logInfo('로컬에 상품 데이터가 없음 - 서버에서 데이터 가져오기 시도', tag: 'ProductNotifier');

        try {
          // DataSyncService를 통해 서버에서 상품 데이터 다운로드
          final dataSyncService = ref.read(dataSyncServiceProvider);
          await dataSyncService.downloadProductsFromFirebase(currentWorkspace.id);

          // 다운로드 후 다시 로컬에서 로드
          products = await repository.getProductsByEventId(currentWorkspace.id);

          LoggerUtils.logInfo('서버에서 상품 데이터 다운로드 완료: ${products.length}개', tag: 'ProductNotifier');
        } catch (e) {
          LoggerUtils.logWarning('서버에서 상품 데이터 가져오기 실패', tag: 'ProductNotifier', error: e);
          // 서버 데이터 가져오기 실패해도 빈 상태로 계속 진행
        }
      }

      if (!mounted) return;

      // 상태 업데이트 최적화
      state = state.copyWith(
        products: products,
        filteredProducts: products,
        isLoading: false,
        errorMessage: null,
      );

      // 판매자 이름 목록 업데이트
      final sellerNames = products
          .map((p) => p.sellerName)
          .where((name) => name != null && name.isNotEmpty)
          .map((name) => name!)
          .toSet()
          .toList()
        ..sort();

      state = state.copyWith(sellerNames: sellerNames);

      // 정렬 및 필터 적용 (품절 상품을 뒤로 밀기 포함)
      _applySortAndFilters();

      stopwatch.stop();
      LoggerUtils.logInfo('상품 로딩 완료: ${products.length}개, 소요시간: ${stopwatch.elapsedMilliseconds}ms', tag: 'ProductNotifier');

    } catch (e) {
      if (!mounted) return;
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
        errorCode: 'PRD_LOAD_ERROR',
        errorSeverity: 'high',
      );
    } finally {
      LoggerUtils.methodEnd('loadProducts', tag: 'ProductNotifier');
    }
  }

  /// 상품 추가 (로컬 DB + Firebase 동기화 포함)
  Future<void> addProduct(Product product) async {
    LoggerUtils.methodStart('addProduct', tag: 'ProductNotifier');
    try {
      // 현재 선택된 행사 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logError('현재 워크스페이스가 null입니다', tag: 'ProductNotifier');
        if (!mounted) return;
        state = state.copyWith(
          errorMessage: '행사 워크스페이스를 선택해주세요',
          errorCode: 'PRD_NO_WORKSPACE_ERROR',
          errorSeverity: 'high',
        );
        throw Exception('현재 워크스페이스가 선택되지 않았습니다');
      }

      LoggerUtils.logInfo('상품 등록 시작 - 워크스페이스: ${currentWorkspace.name} (ID: ${currentWorkspace.id}), 상품: ${product.name}', tag: 'ProductNotifier');

      // 상품에 현재 워크스페이스 ID 설정
      final productWithEventId = product.copyWith(eventId: currentWorkspace.id);

      LoggerUtils.logInfo('상품 등록 데이터 - 이름: ${productWithEventId.name}, 가격: ${productWithEventId.price}, 수량: ${productWithEventId.quantity}, eventId: ${productWithEventId.eventId}', tag: 'ProductNotifier');

      // 1. 로컬 DB에 저장
      await repository.insertProduct(productWithEventId);
      LoggerUtils.logInfo('상품 로컬 저장 성공: ${productWithEventId.name}', tag: 'ProductNotifier');

      // 최근 추가한 상품으로 캐시 (무한 루프 방지용)
      if (productWithEventId.id != null) {
        _recentlyAddedProducts.add(productWithEventId.id!);
        // 5초 후 캐시에서 제거 - 안전하게 Future.delayed 사용
        Future.delayed(const Duration(seconds: 5), () {
          _recentlyAddedProducts.remove(productWithEventId.id!);
        });
      }

      // 2. Firebase에 즉시 업로드 (실시간 동기화를 위해)
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSingleProduct(productWithEventId);
        LoggerUtils.logInfo('상품 Firebase 업로드 성공: ${productWithEventId.name}', tag: 'ProductNotifier');
      } catch (e) {
        LoggerUtils.logError('상품 Firebase 업로드 실패 (로컬 저장은 성공): ${productWithEventId.name}', tag: 'ProductNotifier', error: e);
        // Firebase 업로드 실패해도 로컬 저장은 성공했으므로 계속 진행
      }

      if (!mounted) return;
      await loadProducts(showLoading: false);
    } catch (e) {
      LoggerUtils.logError('상품 등록 실패', tag: 'ProductNotifier', error: e);
      if (!mounted) return;
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'PRD_ADD_ERROR',
        errorSeverity: 'high',
      );
      rethrow; // 에러를 다시 던져서 호출자가 처리할 수 있도록 함
    }
    LoggerUtils.methodEnd('addProduct', tag: 'ProductNotifier');
  }



  /// 상품 수정 (로컬 DB + Firebase 동기화 + 연관 데이터 처리 포함)
  Future<void> updateProduct(Product product, {bool updateRelatedSalesLogs = true}) async {
    LoggerUtils.methodStart('updateProduct', tag: 'ProductNotifier');
    try {
      // 상품명 변경 여부 확인 (실시간 동기화용)
      Product? existingProduct;
      bool nameChanged = false;
      if (updateRelatedSalesLogs && product.id != null) {
        existingProduct = await repository.getProductById(product.id!);
        nameChanged = existingProduct != null && existingProduct.name != product.name;
      }

      // 1. 로컬 DB 업데이트
      await repository.updateProduct(product, updateRelatedSalesLogs: updateRelatedSalesLogs);

      // 2. Firebase에 즉시 업로드 (실시간 동기화)
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSingleProduct(product);
        LoggerUtils.logInfo('상품 Firebase 업데이트 성공: ${product.name}', tag: 'ProductNotifier');

        // 3. 상품명이 변경된 경우 연관된 판매 기록들도 Firebase에 동기화
        if (nameChanged && product.id != null) {
          await _syncRelatedSalesLogsToFirebase(product.id!, product.name);
        }
      } catch (e) {
        LoggerUtils.logError('상품 Firebase 업데이트 실패 (로컬 업데이트는 성공): ${product.name}', tag: 'ProductNotifier', error: e);
        // Firebase 업데이트 실패해도 로컬 업데이트는 성공했으므로 계속 진행
      }

      if (!mounted) return;
      await loadProducts(showLoading: false);
    } catch (e) {
      if (!mounted) return;
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'PRD_UPDATE_ERROR',
        errorSeverity: 'high',
      );
    }
    LoggerUtils.methodEnd('updateProduct', tag: 'ProductNotifier');
  }



  /// 상품명 변경 시 연관된 판매 기록들을 Firebase에 동기화
  Future<void> _syncRelatedSalesLogsToFirebase(int productId, String newProductName) async {
    try {
      LoggerUtils.logInfo('상품명 변경으로 인한 연관 판매 기록 Firebase 동기화 시작: $newProductName', tag: 'ProductNotifier');

      // 해당 상품의 모든 판매 기록 조회 (SalesLogNotifier 사용 안 함)
      final salesLogRepository = SalesLogRepository(database: ref.read(databaseServiceProvider));
      final relatedSalesLogs = await salesLogRepository.getSalesLogsByProductId(productId);

      if (relatedSalesLogs.isNotEmpty) {
        final dataSyncService = ref.read(dataSyncServiceProvider);

        // 각 판매 기록을 Firebase에 동기화
        for (final salesLog in relatedSalesLogs) {
          try {
            await dataSyncService.uploadSingleSalesLog(salesLog);
          } catch (e) {
            LoggerUtils.logWarning('판매 기록 Firebase 동기화 실패: ID ${salesLog.id}', tag: 'ProductNotifier', error: e);
            // 개별 실패는 로그만 남기고 계속 진행
          }
        }

        LoggerUtils.logInfo('연관 판매 기록 Firebase 동기화 완료: ${relatedSalesLogs.length}개', tag: 'ProductNotifier');
      }
    } catch (e) {
      LoggerUtils.logError('연관 판매 기록 Firebase 동기화 실패', tag: 'ProductNotifier', error: e);
      // 실패해도 상품 업데이트는 성공했으므로 예외를 던지지 않음
    }
  }

  /// 상품과 연관된 판매 기록을 함께 삭제 (SalesLogNotifier 사용 안 함)
  Future<void> deleteProductWithSalesLogs(Product product, List<SalesLog> salesLogsToDelete) async {
    LoggerUtils.methodStart('deleteProductWithSalesLogs', tag: 'ProductNotifier');
    try {
      // 현재 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        throw Exception('현재 워크스페이스가 선택되지 않았습니다.');
      }

      // 상품의 eventId와 현재 워크스페이스 ID 일치 확인
      if (product.eventId != currentWorkspace.id) {
        LoggerUtils.logWarning(
          '상품의 eventId(${product.eventId})와 현재 워크스페이스 ID(${currentWorkspace.id})가 일치하지 않습니다. 현재 워크스페이스 기준으로 삭제를 진행합니다.',
          tag: 'ProductNotifier'
        );
        product = product.copyWith(eventId: currentWorkspace.id);
      }

      LoggerUtils.logInfo('상품과 판매 기록 함께 삭제 시작: ${product.name} (ID: ${product.id}, 판매기록: ${salesLogsToDelete.length}개)', tag: 'ProductNotifier');

      // 최근 삭제한 상품으로 캐시 (무한 루프 방지용)
      if (product.id != null) {
        _recentlyDeletedProducts.add(product.id!);
        Future.delayed(const Duration(seconds: 5), () {
          _recentlyDeletedProducts.remove(product.id!);
        });
      }

      // 1. 연관된 판매 기록들을 먼저 삭제 (SalesLogRepository 직접 사용)
      final salesLogRepository = SalesLogRepository(database: ref.read(databaseServiceProvider));
      final dataSyncService = ref.read(dataSyncServiceProvider);

      for (final salesLog in salesLogsToDelete) {
        try {
          // 로컬 DB에서 판매 기록 삭제
          await salesLogRepository.deleteSalesLog(salesLog.id);
          LoggerUtils.logInfo('판매 기록 로컬 삭제 완료: ID ${salesLog.id}', tag: 'ProductNotifier');

          // Firebase에서 판매 기록 삭제
          try {
            await dataSyncService.deleteSingleSalesLog(salesLog);
            LoggerUtils.logInfo('판매 기록 Firebase 삭제 완료: ID ${salesLog.id}', tag: 'ProductNotifier');
          } catch (e) {
            LoggerUtils.logWarning('판매 기록 Firebase 삭제 실패 (로컬 삭제는 성공): ID ${salesLog.id}', tag: 'ProductNotifier', error: e);
          }
        } catch (e) {
          LoggerUtils.logError('판매 기록 삭제 실패: ID ${salesLog.id}', tag: 'ProductNotifier', error: e);
          // 개별 판매 기록 삭제 실패해도 계속 진행
        }
      }

      // 2. 상품 삭제
      await repository.deleteProduct(product);
      LoggerUtils.logInfo('상품 로컬 DB 삭제 완료: ${product.name}', tag: 'ProductNotifier');

      // 3. Firebase에서 상품 삭제
      try {
        await dataSyncService.deleteSingleProduct(product);
        LoggerUtils.logInfo('상품 Firebase 삭제 성공: ${product.name}', tag: 'ProductNotifier');
      } catch (e) {
        LoggerUtils.logError('상품 Firebase 삭제 실패 (로컬 삭제는 성공): ${product.name}', tag: 'ProductNotifier', error: e);
      }

      // 4. 상태 업데이트
      if (!mounted) return;
      await loadProducts(showLoading: false);

      LoggerUtils.logInfo('상품과 판매 기록 함께 삭제 완료: ${product.name}', tag: 'ProductNotifier');
    } catch (e) {
      if (!mounted) return;
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'PRD_DELETE_WITH_SALES_ERROR',
        errorSeverity: 'high',
      );
      LoggerUtils.logError('상품과 판매 기록 함께 삭제 실패: ${product.name}', tag: 'ProductNotifier', error: e);
      rethrow;
    }
    LoggerUtils.methodEnd('deleteProductWithSalesLogs', tag: 'ProductNotifier');
  }

  /// 상품과 연관된 판매 기록 조회 (SalesLogNotifier 사용 안 함)
  Future<List<SalesLog>> getRelatedSalesLogs(int productId) async {
    try {
      final salesLogRepository = SalesLogRepository(database: ref.read(databaseServiceProvider));
      return await salesLogRepository.getSalesLogsByProductId(productId);
    } catch (e) {
      LoggerUtils.logError('연관된 판매 기록 조회 실패: productId $productId', tag: 'ProductNotifier', error: e);
      return [];
    }
  }

  /// 상품 삭제 (로컬 DB + Firebase 동기화 + 연관 데이터 처리 포함)
  Future<void> deleteProduct(Product product) async {
    LoggerUtils.methodStart('deleteProduct', tag: 'ProductNotifier');
    try {
      // 현재 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        throw Exception('현재 워크스페이스가 선택되지 않았습니다.');
      }

      // 상품의 eventId와 현재 워크스페이스 ID 일치 확인
      if (product.eventId != currentWorkspace.id) {
        LoggerUtils.logWarning(
          '상품의 eventId(${product.eventId})와 현재 워크스페이스 ID(${currentWorkspace.id})가 일치하지 않습니다. 현재 워크스페이스 기준으로 삭제를 진행합니다.',
          tag: 'ProductNotifier'
        );
        // 현재 워크스페이스 ID로 업데이트된 상품 객체 생성
        product = product.copyWith(eventId: currentWorkspace.id);
      }

      LoggerUtils.logInfo('상품 삭제 시작: ${product.name} (ID: ${product.id}, EventID: ${product.eventId})', tag: 'ProductNotifier');

      // 최근 삭제한 상품으로 캐시 (무한 루프 방지용)
      if (product.id != null) {
        _recentlyDeletedProducts.add(product.id!);
        // 5초 후 캐시에서 제거
        Future.delayed(const Duration(seconds: 5), () {
          _recentlyDeletedProducts.remove(product.id!);
        });
      }

      // 1. 연관된 판매 기록 확인 및 안전 처리 (SalesLogNotifier 사용 안 함)
      try {
        final salesLogRepository = SalesLogRepository(database: ref.read(databaseServiceProvider));
        final relatedSalesLogs = await salesLogRepository.getSalesLogsByProductId(product.id!);

        if (relatedSalesLogs.isNotEmpty) {
          LoggerUtils.logInfo('상품 삭제 전 연관된 판매 기록 ${relatedSalesLogs.length}개 확인됨: ${product.name}', tag: 'ProductNotifier');
          // 판매 기록은 유지하되, 상품이 삭제되었음을 로그로만 기록
          // 실제 판매 기록 삭제는 사용자가 명시적으로 요청할 때만 수행
        }
      } catch (e) {
        LoggerUtils.logWarning('연관된 판매 기록 확인 실패 (삭제는 계속 진행): ${product.name}', tag: 'ProductNotifier', error: e);
      }

      // 2. 로컬 DB에서 삭제
      await repository.deleteProduct(product);
      LoggerUtils.logInfo('상품 로컬 DB 삭제 완료: ${product.name}', tag: 'ProductNotifier');

      // 3. Firebase에서도 삭제 (실시간 동기화를 위해)
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.deleteSingleProduct(product);
        LoggerUtils.logInfo('상품 Firebase 삭제 성공: ${product.name}', tag: 'ProductNotifier');
      } catch (e) {
        LoggerUtils.logError('상품 Firebase 삭제 실패 (로컬 삭제는 성공): ${product.name}', tag: 'ProductNotifier', error: e);
        // Firebase 삭제 실패해도 로컬 삭제는 성공했으므로 계속 진행
      }

      // 4. 상태 업데이트
      if (!mounted) return;
      await loadProducts(showLoading: false);
      LoggerUtils.logInfo('상품 삭제 완료: ${product.name}', tag: 'ProductNotifier');
    } catch (e) {
      LoggerUtils.logError('상품 삭제 실패: ${product.name}', tag: 'ProductNotifier', error: e);

      // 삭제 실패 시 캐시에서 제거 (재시도 가능하도록)
      if (product.id != null) {
        _recentlyDeletedProducts.remove(product.id!);
      }

      if (!mounted) return;
      state = state.copyWith(
        errorMessage: '상품 삭제 중 오류가 발생했습니다: ${e.toString()}',
        errorCode: 'PRD_DELETE_ERROR',
        errorSeverity: 'high',
      );
    }
    LoggerUtils.methodEnd('deleteProduct', tag: 'ProductNotifier');
  }



  // UI에서 필요한 추가 메서드들
  
  Future<Product?> getProductById(int id) async {
    try {
      return await repository.getProductById(id);
    } catch (e) {
      LoggerUtils.logError('Error getting product by id: $e', tag: 'ProductNotifier');
      return null;
    }
  }

  Future<void> updateProductStock(int productId, int newStock) async {
    try {
      final product = await repository.getProductById(productId);
      if (product != null) {
        final updatedProduct = product.copyWith(quantity: newStock);
        await updateProduct(updatedProduct);
      }
    } catch (e) {
      if (!mounted) return;
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'PRD_STOCK_UPDATE_ERROR',
        errorSeverity: 'high',
      );
    }
  }

  Future<void> increaseStock(int productId, int amount) async {
    try {
      final product = await repository.getProductById(productId);
      if (product != null) {
        final newStock = product.quantity + amount;
        await updateProductStock(productId, newStock);
      } else {
        if (!mounted) return;
        state = state.copyWith(
          errorMessage: '해당 상품이 이미 삭제되어 재고를 복원할 수 없습니다.',
          errorCode: 'PRD_NOT_FOUND_ERROR',
          errorSeverity: 'high',
        );
      }
    } catch (e) {
      if (!mounted) return;
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'PRD_STOCK_INCREASE_ERROR',
        errorSeverity: 'high',
      );
    }
  }

  Future<void> decreaseStock(int productId, int amount) async {
    try {
      final product = await repository.getProductById(productId);
      if (product != null) {
        final newStock = product.quantity - amount;
        if (newStock >= 0) {
          await updateProductStock(productId, newStock);
        } else {
          if (!mounted) return;
          state = state.copyWith(
            errorMessage: '재고가 부족합니다. 현재 재고: ${product.quantity}, 요청 수량: $amount',
            errorCode: 'PRD_INSUFFICIENT_STOCK_ERROR',
            errorSeverity: 'high',
          );
        }
      } else {
        if (!mounted) return;
        state = state.copyWith(
          errorMessage: '해당 상품이 이미 삭제되어 재고를 차감할 수 없습니다.',
          errorCode: 'PRD_NOT_FOUND_ERROR',
          errorSeverity: 'high',
        );
      }
    } catch (e) {
      if (!mounted) return;
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'PRD_STOCK_DECREASE_ERROR',
        errorSeverity: 'high',
      );
    }
  }

  // ====== 정렬/필터/검색 로직 완전 새 구현 + 안정성 큐잉 추가 ======

  bool _isProcessing = false;
  VoidCallback? _pendingRequest;

  void setSortOption(ProductSortOption sortOption) {
    if (!mounted) return;
    final allowed = [
      ProductSortOption.recentlyAdded,
      ProductSortOption.nameAsc, ProductSortOption.nameDesc,
      ProductSortOption.priceAsc, ProductSortOption.priceDesc,
      ProductSortOption.quantityAsc, ProductSortOption.quantityDesc,
    ];
    final next = allowed.contains(sortOption) ? sortOption : ProductSortOption.recentlyAdded;
    state = state.copyWith(currentSortOption: next);
    _requestApplySortAndFilters();
  }

  void setSellerFilter(String sellerName) {
    if (!mounted) return;
    state = state.copyWith(selectedSellerFilter: sellerName);
    _requestApplySortAndFilters();
  }

  void searchProducts(String query) async {
    if (!mounted) return;
    _searchQuery = query;
    _debounceTimer?.cancel();
    
    // Timer 대신 Future.delayed 사용 (안전한 비동기 처리)
    await Future.delayed(const Duration(milliseconds: 300));
    if (!mounted) return;
    _requestApplySortAndFilters();
  }

  void _requestApplySortAndFilters() {
    if (_isProcessing) {
      _pendingRequest = _applySortAndFilters;
      return;
    }
    _applySortAndFilters();
  }

  void _applySortAndFilters() {
    if (!mounted) return;
    _isProcessing = true;
    List<Product> filtered = List.from(state.products);

    // 판매자 필터 적용
    if (state.selectedSellerFilter.isNotEmpty) {
      filtered = filtered.where((product) => product.sellerName == state.selectedSellerFilter).toList();
    }

    // 검색 필터 적용
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((product) =>
        product.name.toLowerCase().contains(query) ||
        (product.sellerName?.toLowerCase().contains(query) ?? false)
      ).toList();
    }

    // 정렬 적용 (품절 상품을 뒤로 밀기 + 기존 정렬 로직)
    filtered.sort((a, b) {
      // 품절 상품을 항상 뒤로 밀기 (재고가 0인 상품)
      final aOutOfStock = a.quantity <= 0;
      final bOutOfStock = b.quantity <= 0;

      if (aOutOfStock && !bOutOfStock) {
        return 1; // a가 품절이면 뒤로
      } else if (!aOutOfStock && bOutOfStock) {
        return -1; // b가 품절이면 a가 앞으로
      }

      // 둘 다 품절이거나 둘 다 재고가 있는 경우 기존 정렬 로직 적용
      switch (state.currentSortOption) {
        case ProductSortOption.nameAsc:
          return a.name.compareTo(b.name);
        case ProductSortOption.nameDesc:
          return b.name.compareTo(a.name);
        case ProductSortOption.priceAsc:
          return a.price.compareTo(b.price);
        case ProductSortOption.priceDesc:
          return b.price.compareTo(a.price);
        case ProductSortOption.quantityAsc:
          return a.quantity.compareTo(b.quantity);
        case ProductSortOption.quantityDesc:
          return b.quantity.compareTo(a.quantity);
        case ProductSortOption.recentlyAdded:
        default:
          return (b.id ?? 0).compareTo(a.id ?? 0);
      }
    });

    state = state.copyWith(filteredProducts: filtered);
    _isProcessing = false;
    if (_pendingRequest != null) {
      final pending = _pendingRequest;
      _pendingRequest = null;
      pending?.call();
    }
  }
  // ====== 정렬/필터/검색 로직 완전 새 구현 + 안정성 큐잉 끝 ======

  void clearError() {
    if (!mounted) return;
    state = state.copyWith(
      errorMessage: null,
      errorCode: null,
      errorSeverity: null,
    );
  }

  @override
  void dispose() {
    // Timer 제거로 메모리 누수 방지
    _debounceTimer?.cancel();
    _realtimeSubscription?.cancel(); // 실시간 동기화 구독 해제
    _firestoreSubscription?.cancel(); // 직접 Firestore 구독 해제
    super.dispose();
  }
}
