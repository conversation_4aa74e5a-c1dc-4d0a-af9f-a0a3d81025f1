import 'package:flutter/material.dart';

class RegistrationCompletePage extends StatelessWidget {
  final String title;
  final String? description;
  final VoidCallback? onConfirm;
  final VoidCallback? onClose;

  const RegistrationCompletePage({
    super.key,
    this.title = '등록이 완료되었습니다!',
    this.description,
    this.onConfirm,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(isTablet ? 32.0 : 24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 성공 아이콘 (심플한 디자인)
              Container(
                width: isTablet ? 120.0 : 100.0,
                height: isTablet ? 120.0 : 100.0,
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check_circle,
                  color: Colors.green.shade600,
                  size: isTablet ? 80.0 : 64.0,
                ),
              ),
              SizedBox(height: isTablet ? 32.0 : 24.0),

              // 제목
              Text(
                title,
                style: TextStyle(
                  fontSize: isTablet ? 28.0 : 24.0,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
                textAlign: TextAlign.center,
              ),

              // 설명
              if (description != null) ...[
                SizedBox(height: isTablet ? 16.0 : 12.0),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isTablet ? 24.0 : 16.0,
                    vertical: isTablet ? 16.0 : 12.0,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Text(
                    description!,
                    style: TextStyle(
                      fontSize: isTablet ? 16.0 : 14.0,
                      color: Colors.grey.shade600,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],

              SizedBox(height: isTablet ? 48.0 : 40.0),

              // 확인 버튼 (심플한 디자인)
              SizedBox(
                width: double.infinity,
                height: isTablet ? 56.0 : 48.0,
                child: ElevatedButton(
                  onPressed: onConfirm ?? () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    '확인',
                    style: TextStyle(
                      fontSize: isTablet ? 18.0 : 16.0,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              // 닫기 버튼 (텍스트 버튼)
              SizedBox(height: isTablet ? 16.0 : 12.0),
              TextButton(
                onPressed: onClose ?? () => Navigator.of(context).pop(),
                child: Text(
                  '닫기',
                  style: TextStyle(
                    fontSize: isTablet ? 16.0 : 14.0,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 