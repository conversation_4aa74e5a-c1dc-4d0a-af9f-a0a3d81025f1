import '../../models/product.dart';
import '../../repositories/product_repository.dart';
import '../../utils/logger_utils.dart';
import '../../utils/provider_exception.dart';

/// 상품 CRUD 작업 로직을 담당하는 클래스
///
/// 주요 기능:
/// - 상품 추가/수정/삭제
/// - 상품 조회
/// - 재고 관리
/// - 에러 처리
class ProductCrudLogic {
  static const String _tag = 'ProductCrudLogic';
  static const String _domain = 'PRD';

  /// 상품을 추가합니다.
  ///
  /// [repository]: 상품 저장소
  /// [product]: 추가할 상품 정보
  /// 반환값: 추가된 상품 ID
  static Future<int> addProduct(
    ProductRepository repository,
    Product product,
  ) async {
    LoggerUtils.methodStart('addProduct', tag: _tag);
    
    try {
      final productId = await repository.insertProduct(product);
      
      LoggerUtils.logInfo(
        'Product added successfully: ${product.name} (ID: $productId)',
        tag: _tag,
      );
      
      return productId;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to add product: ${product.name}',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      
      throw ProviderException(
        message: '상품 추가에 실패했습니다',
        code: '${_domain}_ADD_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      LoggerUtils.methodEnd('addProduct', tag: _tag);
    }
  }

  /// 상품을 업데이트합니다.
  ///
  /// [repository]: 상품 저장소
  /// [product]: 업데이트할 상품 정보
  static Future<void> updateProduct(
    ProductRepository repository,
    Product product,
  ) async {
    LoggerUtils.methodStart('updateProduct', tag: _tag);
    
    try {
      await repository.updateProduct(product);
      
      LoggerUtils.logInfo(
        'Product updated successfully: ${product.name} (ID: ${product.id})',
        tag: _tag,
      );
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to update product: ${product.name} (ID: ${product.id})',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      
      throw ProviderException(
        message: '상품 수정에 실패했습니다',
        code: '${_domain}_UPDATE_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      LoggerUtils.methodEnd('updateProduct', tag: _tag);
    }
  }

  /// 상품을 삭제합니다.
  ///
  /// [repository]: 상품 저장소
  /// [product]: 삭제할 상품 정보
  static Future<void> deleteProduct(
    ProductRepository repository,
    Product product,
  ) async {
    LoggerUtils.methodStart('deleteProduct', tag: _tag);
    
    try {
      await repository.deleteProduct(product);
      
      LoggerUtils.logInfo(
        'Product deleted successfully: ${product.name} (ID: ${product.id})',
        tag: _tag,
      );
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to delete product: ${product.name} (ID: ${product.id})',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      
      throw ProviderException(
        message: '상품 삭제에 실패했습니다',
        code: '${_domain}_DELETE_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      LoggerUtils.methodEnd('deleteProduct', tag: _tag);
    }
  }

  /// ID로 상품을 조회합니다.
  ///
  /// [repository]: 상품 저장소
  /// [id]: 조회할 상품 ID
  /// 반환값: Product 객체 또는 null
  static Future<Product?> getProductById(
    ProductRepository repository,
    int id,
  ) async {
    LoggerUtils.methodStart('getProductById', tag: _tag);
    
    try {
      final product = await repository.getProductById(id);
      
      LoggerUtils.logInfo(
        'Product retrieved successfully: ID $id',
        tag: _tag,
      );
      
      return product;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get product: ID $id',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      
      throw ProviderException(
        message: '상품 조회에 실패했습니다',
        code: '${_domain}_GET_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      LoggerUtils.methodEnd('getProductById', tag: _tag);
    }
  }

  /// 모든 상품을 조회합니다.
  ///
  /// [repository]: 상품 저장소
  /// 반환값: Product 객체 리스트
  static Future<List<Product>> getAllProducts(
    ProductRepository repository,
  ) async {
    LoggerUtils.methodStart('getAllProducts', tag: _tag);
    
    try {
      final products = await repository.getAllProducts();
      
      LoggerUtils.logInfo(
        'All products retrieved successfully: ${products.length} products',
        tag: _tag,
      );
      
      return products;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to get all products',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      
      throw ProviderException(
        message: '상품 목록 조회에 실패했습니다',
        code: '${_domain}_GET_ALL_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      LoggerUtils.methodEnd('getAllProducts', tag: _tag);
    }
  }

  /// 상품 재고를 업데이트합니다.
  ///
  /// [repository]: 상품 저장소
  /// [productId]: 상품 ID
  /// [newQuantity]: 새로운 재고 수량
  static Future<void> updateProductStock(
    ProductRepository repository,
    int productId,
    int newQuantity,
  ) async {
    LoggerUtils.methodStart('updateProductStock', tag: _tag);
    
    try {
      await repository.updateProductStock(productId, newQuantity);
      
      LoggerUtils.logInfo(
        'Product stock updated successfully: ID $productId, new quantity: $newQuantity',
        tag: _tag,
      );
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to update product stock: ID $productId, new quantity: $newQuantity',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      
      throw ProviderException(
        message: '상품 재고 수정에 실패했습니다',
        code: '${_domain}_STOCK_UPDATE_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      LoggerUtils.methodEnd('updateProductStock', tag: _tag);
    }
  }

  /// 상품 재고를 증가시킵니다.
  ///
  /// [repository]: 상품 저장소
  /// [productId]: 상품 ID
  /// [quantity]: 증가할 수량
  static Future<void> increaseStock(
    ProductRepository repository,
    int productId,
    int quantity,
  ) async {
    LoggerUtils.methodStart('increaseStock', tag: _tag);
    
    try {
      await repository.increaseStock(productId, quantity);
      
      LoggerUtils.logInfo(
        'Product stock increased successfully: ID $productId, quantity: +$quantity',
        tag: _tag,
      );
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to increase product stock: ID $productId, quantity: +$quantity',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      
      throw ProviderException(
        message: '상품 재고 증가에 실패했습니다',
        code: '${_domain}_STOCK_INCREASE_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      LoggerUtils.methodEnd('increaseStock', tag: _tag);
    }
  }

  /// 상품 재고를 감소시킵니다.
  ///
  /// [repository]: 상품 저장소
  /// [productId]: 상품 ID
  /// [quantity]: 감소할 수량
  static Future<void> decreaseStock(
    ProductRepository repository,
    int productId,
    int quantity,
  ) async {
    LoggerUtils.methodStart('decreaseStock', tag: _tag);
    
    try {
      await repository.decreaseStock(productId, quantity);
      
      LoggerUtils.logInfo(
        'Product stock decreased successfully: ID $productId, quantity: -$quantity',
        tag: _tag,
      );
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to decrease product stock: ID $productId, quantity: -$quantity',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      
      throw ProviderException(
        message: '상품 재고 감소에 실패했습니다',
        code: '${_domain}_STOCK_DECREASE_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      LoggerUtils.methodEnd('decreaseStock', tag: _tag);
    }
  }

  /// 상품 목록을 새로고침합니다.
  ///
  /// [repository]: 상품 저장소
  /// 반환값: 새로고침된 상품 목록
  static Future<List<Product>> refreshProducts(
    ProductRepository repository,
  ) async {
    LoggerUtils.methodStart('refreshProducts', tag: _tag);
    
    try {
      final products = await getAllProducts(repository);
      
      LoggerUtils.logInfo(
        'Products refreshed successfully: ${products.length} products',
        tag: _tag,
      );
      
      return products;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Failed to refresh products',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      
      throw ProviderException(
        message: '상품 목록 새로고침에 실패했습니다',
        code: '${_domain}_REFRESH_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      LoggerUtils.methodEnd('refreshProducts', tag: _tag);
    }
  }
} 