import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/product.dart';
import '../../providers/sale_provider.dart';
import '../../utils/logger_utils.dart';

/// 판매 화면의 비즈니스 로직을 처리하는 클래스입니다.
class SaleBusinessLogic {
  final WidgetRef ref;
  static const String _tag = 'SaleBusinessLogic';

  SaleBusinessLogic(this.ref);

  void dispose() {
    // 필요한 경우 정리 작업 수행
  }

  void onProductSelected(Product product) {
    LoggerUtils.methodStart('onProductSelected', tag: _tag);
    ref.read(saleNotifierProvider.notifier).addProduct(product);
    LoggerUtils.methodEnd('onProductSelected', tag: _tag);
  }

  void onQuantityChanged(int productId, int quantity) {
    LoggerUtils.methodStart('onQuantityChanged', tag: _tag);
    ref.read(saleNotifierProvider.notifier).updateProductQuantity(
          productId,
          quantity,
        );
    LoggerUtils.methodEnd('onQuantityChanged', tag: _tag);
  }

  void onProductRemoved(Product product) {
    LoggerUtils.methodStart('onProductRemoved', tag: _tag);
    ref.read(saleNotifierProvider.notifier).removeProduct(product);
    LoggerUtils.methodEnd('onProductRemoved', tag: _tag);
  }

  Future<void> processSale() async {
    LoggerUtils.methodStart('processSale', tag: _tag);
    await ref.read(saleNotifierProvider.notifier).processSale();
    LoggerUtils.methodEnd('processSale', tag: _tag);
  }
} 