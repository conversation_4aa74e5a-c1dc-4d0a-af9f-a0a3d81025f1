import 'package:flutter/material.dart';
import '../../models/sales_log.dart';
import '../../models/sales_log_display_item.dart';
import '../../models/transaction_type.dart';
import '../../models/category.dart' as model_category;
import '../../utils/currency_utils.dart';
import '../../utils/date_utils.dart' as app_date_utils;
import '../../widgets/confirmation_dialog.dart';

/// 판매 기록 목록의 UI 컴포넌트들을 담당하는 클래스
///
/// 주요 기능:
/// - 개별 판매 기록 아이템 위젯
/// - 그룹 판매 기록 아이템 위젯
/// - 빈 상태 위젯
/// - 다이얼로그 위젯들
class SalesLogListUiComponents {
  /// 빈 상태 위젯
  static Widget buildEmptyState({
    required String selectedSeller,
    required TransactionType? selectedTransactionType,
  }) {
    String message = '판매 기록이 없습니다.';

    if (selectedSeller != '전체 판매자') {
      message = '$selectedSeller의 판매 기록이 없습니다.';
    }

    if (selectedTransactionType != null) {
      message = '${selectedTransactionType.displayName} 기록이 없습니다.';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.receipt_long_outlined, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(fontFamily: 'Pretendard', fontSize: 18, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 표시 아이템 위젯 (개별/그룹 구분)
  static Widget buildSalesLogItem(
    SalesLogDisplayItem displayItem, {
    required Function(SalesLog) onDelete,
    required Function(GroupedSale) onDeleteGroup,
    required Function(GroupedSale) onShowGroupDetail,
    List<model_category.Category>? categories,
    Map<int, String>? productCategoryMap, // productId -> categoryName 매핑
  }) {
    if (displayItem is SingleItem) {
      return buildSingleSalesLogItem(
        displayItem.salesLog,
        onDelete: onDelete,
        categories: categories,
        productCategoryMap: productCategoryMap,
      );
    } else if (displayItem is GroupedSale) {
      return buildGroupedSalesLogItem(
        displayItem,
        onDelete: onDeleteGroup,
        onShowDetail: onShowGroupDetail,
        categories: categories,
        productCategoryMap: productCategoryMap,
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  /// 개별 판매 기록 아이템 위젯
  static Widget buildSingleSalesLogItem(
    SalesLog salesLog, {
    required Function(SalesLog) onDelete,
    List<model_category.Category>? categories,
    Map<int, String>? productCategoryMap, // productId -> categoryName 매핑
  }) {
    final date = DateTime.fromMillisecondsSinceEpoch(salesLog.saleTimestamp);
    final dateStr = app_date_utils.DateUtils.formatKorDateTime(date);

    // 카테고리명과 상품명 조합
    String displayName = salesLog.productName;
    if (productCategoryMap != null && salesLog.productId != null) {
      final categoryName = productCategoryMap[salesLog.productId];
      if (categoryName != null) {
        displayName = '[$categoryName]${salesLog.productName}';
      }
    }

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onLongPress: () => onDelete(salesLog),
          splashColor: Colors.grey.withValues(alpha: 0.1),
          highlightColor: Colors.grey.withValues(alpha: 0.05),
          child: Column(
            children: [
              // 첫 번째 행
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 상품명 (카테고리명 포함)
                  Expanded(
                    child: Text(
                      displayName,
                      style: TextStyle(fontFamily: 'Pretendard', 
                        fontSize: 17,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),

                  // 타임스탬프
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      dateStr,
                      style: TextStyle(fontFamily: 'Pretendard', 
                        fontSize: 12,
                        color: Colors.black54,
                      ),
                    ),
                  ),

                  // 삭제 버튼
                  SizedBox(
                    width: 40,
                    height: 40,
                    child: IconButton(
                      onPressed: () => onDelete(salesLog),
                      icon: const Icon(
                        Icons.delete_outline,
                        size: 20,
                        color: Colors.red,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),

              // 두 번째 행
              Container(
                margin: const EdgeInsets.only(top: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 판매자 정보
                    Expanded(
                      child: Text(
                        '판매자: ${salesLog.sellerName ?? '알 수 없음'}${salesLog.paymentMethod != null ? ' • 결제: ${salesLog.paymentMethod}' : ''}',
                        style: TextStyle(fontFamily: 'Pretendard', 
                          fontSize: 14,
                          color: Colors.black54,
                        ),
                      ),
                    ),

                    // 수량과 금액 (세트 할인이 있는 경우 아이콘 표시)
                    Row(
                      children: [
                        if (salesLog.setDiscountAmount > 0)
                          Padding(
                            padding: const EdgeInsets.only(right: 4),
                            child: Icon(
                              Icons.local_offer,
                              size: 16,
                              color: Colors.green.shade600,
                            ),
                          ),
                        Text(
                          '${salesLog.soldQuantity}개 • ${CurrencyUtils.formatCurrency(salesLog.totalAmount)}',
                          style: TextStyle(fontFamily: 'Pretendard', 
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 그룹 판매 기록 아이템 위젯
  static Widget buildGroupedSalesLogItem(
    GroupedSale groupedSale, {
    required Function(GroupedSale) onDelete,
    required Function(GroupedSale) onShowDetail,
    List<model_category.Category>? categories,
    Map<int, String>? productCategoryMap, // productId -> categoryName 매핑
  }) {
    final date = DateTime.fromMillisecondsSinceEpoch(
      groupedSale.representativeTimestampMillis(),
    );
    final dateStr = app_date_utils.DateUtils.formatKorDateTime(date);

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        border: Border.all(color: Colors.orange.shade200, width: 1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onShowDetail(groupedSale),
          onLongPress: () => onDelete(groupedSale),
          splashColor: Colors.grey.withValues(alpha: 0.1),
          highlightColor: Colors.grey.withValues(alpha: 0.05),
          child: Column(
            children: [
              // 첫 번째 행: 그룹 제목
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 그룹 제목 (카테고리명 포함)
                  Expanded(
                    child: Text(
                      _buildGroupTitleWithCategory(groupedSale, productCategoryMap),
                      style: TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ),

                  // 타임스탬프
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      dateStr,
                      style: TextStyle(fontFamily: 'Pretendard', 
                        fontSize: 12,
                        color: Colors.black54,
                      ),
                    ),
                  ),

                  // 삭제 버튼
                  SizedBox(
                    width: 40,
                    height: 40,
                    child: IconButton(
                      onPressed: () => onDelete(groupedSale),
                      icon: const Icon(
                        Icons.delete_outline,
                        size: 20,
                        color: Colors.red,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),

              // 두 번째 행: 그룹 요약
              Container(
                margin: const EdgeInsets.only(top: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 판매자 정보
                    Expanded(
                      child: Text(
                        '판매자: ${groupedSale.sellerDisplayText}',
                        style: TextStyle(fontFamily: 'Pretendard', 
                          fontSize: 14,
                          color: Colors.black54,
                        ),
                      ),
                    ),

                    // 총 수량과 금액 (세트 할인이 있는 경우 아이콘 표시)
                    Row(
                      children: [
                        if (groupedSale.hasSetDiscount)
                          Padding(
                            padding: const EdgeInsets.only(right: 4),
                            child: Icon(
                              Icons.local_offer,
                              size: 16,
                              color: Colors.green.shade600,
                            ),
                          ),
                        Text(
                          '총 ${groupedSale.totalQuantity}개 • ${CurrencyUtils.formatCurrency(groupedSale.totalGroupAmount)}',
                          style: TextStyle(fontFamily: 'Pretendard', 
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }



  /// 삭제 확인 다이얼로그
  static Future<bool> showDeleteConfirmDialog(
    BuildContext context,
    SalesLog salesLog,
  ) async {
    return await ConfirmationDialog.showDelete(
      context: context,
      title: '판매 기록 삭제',
      message: '\'${salesLog.productName}\' (판매자: ${salesLog.sellerName ?? '알 수 없음'}) 판매 기록을 삭제하시겠습니까? ${salesLog.transactionType == TransactionType.sale && salesLog.productId != null ? "삭제 시 재고가 복구됩니다." : "삭제 시 재고가 복구됩니다."}',
    ) ?? false;
  }

  /// 그룹 제목에 카테고리명을 포함하여 생성
  static String _buildGroupTitleWithCategory(
    GroupedSale groupedSale,
    Map<int, String>? productCategoryMap,
  ) {
    if (groupedSale.items.isEmpty) return '';

    // 첫 번째 아이템의 상품명에 카테고리명 추가
    final firstItem = groupedSale.items.first;
    String displayName = firstItem.productName;

    // 카테고리명 추가
    if (productCategoryMap != null && firstItem.productId != null) {
      final categoryName = productCategoryMap[firstItem.productId];
      if (categoryName != null) {
        displayName = '[$categoryName]${firstItem.productName}';
      }
    }

    // 8글자 제한 적용 (카테고리명 포함)
    if (displayName.length <= 8) {
      // 그대로 사용
    } else {
      displayName = '${displayName.substring(0, 8)}..';
    }

    // 여러 종류인 경우 "외 N종류" 추가
    final itemCount = groupedSale.items.length;
    if (itemCount == 1) {
      return displayName;
    } else {
      return '$displayName 외 ${itemCount - 1}종류';
    }
  }

  /// 그룹 삭제 다이얼로그 설명 텍스트 생성
  static String _buildDeleteGroupDescription(
    GroupedSale groupedSale,
    int totalQuantity,
    String totalAmountFormatted,
  ) {
    final itemCount = groupedSale.items.length;
    final sellerText = groupedSale.sellerDisplayText;

    if (itemCount == 1) {
      return '\'${groupedSale.representativeProductNameForDisplay}\' (판매자: $sellerText, 총 $totalQuantity개, $totalAmountFormatted)의 판매 기록을 삭제하시겠습니까? 삭제 시 재고가 복구됩니다.';
    } else {
      return '\'${groupedSale.representativeProductNameForDisplay}\' 외 ${itemCount - 1}종류 (판매자: $sellerText, 총 $totalQuantity개, $totalAmountFormatted)의 묶음 판매 기록을 삭제하시겠습니까? 삭제 시 관련된 모든 판매 기록의 재고가 복구됩니다.';
    }
  }

  /// 그룹 삭제 확인 다이얼로그
  static Future<bool> showDeleteGroupConfirmDialog(
    BuildContext context,
    GroupedSale groupedSale,
  ) async {
    final totalQuantity = groupedSale.totalQuantity;
    final totalAmountFormatted = CurrencyUtils.formatCurrency(
      groupedSale.totalGroupAmount,
    );

    return await ConfirmationDialog.showDelete(
      context: context,
      title: '묶음 판매 기록 삭제',
      message: _buildDeleteGroupDescription(groupedSale, totalQuantity, totalAmountFormatted),
      confirmLabel: '삭제',
      cancelLabel: '취소',
    ) ?? false;
  }
} 


