import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/event.dart';
import '../utils/logger_utils.dart';

/// 현재 선택된 행사를 관리하는 Provider
/// 
/// 앱 전체에서 현재 활성화된 행사 정보를 제공하고 관리합니다.
/// SharedPreferences를 통해 앱 재시작 시에도 선택된 행사를 유지합니다.
class CurrentEventNotifier extends StateNotifier<Event?> {
  static const String _tag = 'CurrentEventProvider';
  static const String _currentEventIdKey = 'current_event_id';

  CurrentEventNotifier() : super(null) {
    _loadCurrentEventId();
  }

  /// SharedPreferences에서 현재 선택된 행사 ID를 로드합니다.
  Future<void> _loadCurrentEventId() async {
    try {
      LoggerUtils.methodStart('_loadCurrentEventId', tag: _tag);
      
      final prefs = await SharedPreferences.getInstance();
      final eventId = prefs.getInt(_currentEventIdKey);
      
      if (eventId != null) {
        LoggerUtils.logInfo('저장된 현재 행사 ID: $eventId', tag: _tag);
        // 실제 Event 객체는 EventProvider에서 로드된 후 설정됩니다.
      } else {
        LoggerUtils.logInfo('저장된 현재 행사 ID가 없습니다', tag: _tag);
      }
      
      LoggerUtils.methodEnd('_loadCurrentEventId', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '현재 행사 ID 로드 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 현재 선택된 행사를 설정합니다.
  Future<void> setCurrentEvent(Event? event) async {
    try {
      LoggerUtils.methodStart('setCurrentEvent', tag: _tag, data: {
        'eventId': event?.id,
        'eventName': event?.name,
        'eventIsNull': event == null,
      });

      // 이전 상태와 다른 경우에만 업데이트
      final previousEvent = state;
      if (previousEvent?.id != event?.id) {
        // 상태 변경을 여러 번 시도하여 확실히 알림
        for (int i = 0; i < 3; i++) {
          state = event;
          await Future.delayed(const Duration(milliseconds: 10));
        }
        LoggerUtils.logInfo('현재 행사 state 업데이트 완료 (${event?.name})', tag: _tag);
      }

      // SharedPreferences에 저장
      final prefs = await SharedPreferences.getInstance();
      if (event != null && event.id != null) {
        await prefs.setInt(_currentEventIdKey, event.id!);
        LoggerUtils.logInfo('현재 행사 설정: ${event.name} (ID: ${event.id})', tag: _tag);
      } else {
        await prefs.remove(_currentEventIdKey);
        LoggerUtils.logInfo('현재 행사 해제', tag: _tag);
      }

      LoggerUtils.methodEnd('setCurrentEvent', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '현재 행사 설정 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {
          'eventId': event?.id,
          'eventName': event?.name,
        },
      );
    }
  }

  /// 저장된 현재 행사 ID를 반환합니다.
  Future<int?> getSavedCurrentEventId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_currentEventIdKey);
    } catch (e) {
      LoggerUtils.logError('저장된 현재 행사 ID 조회 실패', tag: _tag, error: e);
      return null;
    }
  }

  /// 현재 행사가 설정되어 있는지 확인합니다.
  bool get hasCurrentEvent => state != null;

  /// 현재 행사 ID를 반환합니다.
  int? get currentEventId => state?.id;

  /// 현재 행사 이름을 반환합니다.
  String get currentEventName => state?.name ?? '행사 없음';

  /// 현재 행사가 진행 중인지 확인합니다.
  bool get isCurrentEventOngoing => state?.isOngoing ?? false;

  /// 현재 행사가 종료되었는지 확인합니다.
  bool get isCurrentEventEnded => state?.isEnded ?? false;

  /// 현재 행사가 예정된 행사인지 확인합니다.
  bool get isCurrentEventUpcoming => state?.isUpcoming ?? false;

  /// 현재 행사의 상태 문자열을 반환합니다.
  String get currentEventStatus => state?.statusString ?? '알 수 없음';

  /// 현재 행사의 날짜 범위 문자열을 반환합니다.
  String get currentEventDateRange => state?.dateRangeString ?? '';
}

/// 현재 선택된 행사 Provider
final currentEventProvider = StateNotifierProvider<CurrentEventNotifier, Event?>(
  (ref) => CurrentEventNotifier(),
);

/// 현재 행사 ID만 필요한 경우를 위한 Provider
final currentEventIdProvider = Provider<int?>((ref) {
  final currentEvent = ref.watch(currentEventProvider);
  return currentEvent?.id;
});

/// 현재 행사 이름만 필요한 경우를 위한 Provider
final currentEventNameProvider = Provider<String>((ref) {
  final currentEvent = ref.watch(currentEventProvider);
  return currentEvent?.name ?? '행사 없음';
});

/// 현재 행사가 설정되어 있는지 확인하는 Provider
final hasCurrentEventProvider = Provider<bool>((ref) {
  final currentEvent = ref.watch(currentEventProvider);
  return currentEvent != null;
});

/// 현재 행사의 상태를 확인하는 Provider
final currentEventStatusProvider = Provider<String>((ref) {
  final currentEvent = ref.watch(currentEventProvider);
  return currentEvent?.statusString ?? '알 수 없음';
});

/// 현재 행사의 날짜 범위를 제공하는 Provider
final currentEventDateRangeProvider = Provider<String>((ref) {
  final currentEvent = ref.watch(currentEventProvider);
  return currentEvent?.dateRangeString ?? '';
});

/// 현재 행사가 진행 중인지 확인하는 Provider
final isCurrentEventOngoingProvider = Provider<bool>((ref) {
  final currentEvent = ref.watch(currentEventProvider);
  return currentEvent?.isOngoing ?? false;
});

/// 현재 행사가 종료되었는지 확인하는 Provider
final isCurrentEventEndedProvider = Provider<bool>((ref) {
  final currentEvent = ref.watch(currentEventProvider);
  return currentEvent?.isEnded ?? false;
});

/// 현재 행사가 예정된 행사인지 확인하는 Provider
final isCurrentEventUpcomingProvider = Provider<bool>((ref) {
  final currentEvent = ref.watch(currentEventProvider);
  return currentEvent?.isUpcoming ?? false;
});
