import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/utils/currency_utils.dart';

void main() {
  group('CurrencyUtils Tests', () {
    group('포맷팅 메서드 테스트', () {
      test('formatCurrency - 기본 콤마 포맷', () {
        expect(CurrencyUtils.formatCurrency(0), equals('0'));
        expect(CurrencyUtils.formatCurrency(1000), equals('1,000'));
        expect(CurrencyUtils.formatCurrency(10000), equals('10,000'));
        expect(CurrencyUtils.formatCurrency(123456), equals('123,456'));
        expect(CurrencyUtils.formatCurrency(1234567), equals('1,234,567'));
        expect(CurrencyUtils.formatCurrency(-1000), equals('-1,000'));
      });

      test('formatCurrencyWithSymbol - 원화 기호 포함', () {
        expect(CurrencyUtils.formatCurrencyWithSymbol(0), equals('￦0'));
        expect(CurrencyUtils.formatCurrencyWithSymbol(1000), equals('￦1,000'));
        expect(
          CurrencyUtils.formatCurrencyWithSymbol(123456),
          equals('￦123,456'),
        );
        expect(
          CurrencyUtils.formatCurrencyWithSymbol(-1000),
          equals('-￦1,000'),
        );
      });

      test('formatCurrencyWithWon - 원 단위 포함', () {
        expect(CurrencyUtils.formatCurrencyWithWon(0), equals('0원'));
        expect(CurrencyUtils.formatCurrencyWithWon(1000), equals('1,000원'));
        expect(CurrencyUtils.formatCurrencyWithWon(123456), equals('123,456원'));
        expect(CurrencyUtils.formatCurrencyWithWon(-1000), equals('-1,000원'));
      });

      test('formatWon - 기존 호환성 메서드', () {
        expect(
          CurrencyUtils.formatWon(1000),
          equals(CurrencyUtils.formatCurrencyWithWon(1000)),
        );
        expect(CurrencyUtils.formatWon(123456), equals('123,456원'));
      });

      test('formatCurrencyWithSign - 부호 포함 포맷', () {
        expect(CurrencyUtils.formatCurrencyWithSign(1000), equals('+1,000원'));
        expect(CurrencyUtils.formatCurrencyWithSign(-1000), equals('-1,000원'));
        expect(CurrencyUtils.formatCurrencyWithSign(0), equals('0원'));
      });

      test('formatSimpleAmount - 간단한 금액 표시', () {
        expect(CurrencyUtils.formatSimpleAmount(5000), equals('5,000원'));
        expect(CurrencyUtils.formatSimpleAmount(10000), equals('1만원'));
        expect(CurrencyUtils.formatSimpleAmount(15000), equals('1만 5,000원'));
        expect(CurrencyUtils.formatSimpleAmount(20000), equals('2만원'));
        expect(CurrencyUtils.formatSimpleAmount(123456), equals('12만 3,456원'));
        expect(CurrencyUtils.formatSimpleAmount(100000), equals('10만원'));
      });
    });

    group('파싱 메서드 테스트', () {
      test('parseCurrency - 문자열을 정수로 변환', () {
        expect(CurrencyUtils.parseCurrency('1000'), equals(1000));
        expect(CurrencyUtils.parseCurrency('1,000'), equals(1000));
        expect(CurrencyUtils.parseCurrency('1,234,567'), equals(1234567));
        expect(CurrencyUtils.parseCurrency('₩1,000'), equals(1000));
        expect(CurrencyUtils.parseCurrency('1,000원'), equals(1000));
        expect(CurrencyUtils.parseCurrency('₩1,234원'), equals(1234));
        expect(CurrencyUtils.parseCurrency('  1,000  '), equals(1000));
      });

      test('parseCurrency - 잘못된 입력 처리', () {
        expect(CurrencyUtils.parseCurrency(''), isNull);
        expect(CurrencyUtils.parseCurrency('   '), isNull);
        expect(CurrencyUtils.parseCurrency('abc'), isNull);
        expect(CurrencyUtils.parseCurrency('1,000.50'), isNull);
        expect(CurrencyUtils.parseCurrency('1000a'), isNull);
      });
    });

    group('검증 메서드 테스트', () {
      test('isPositive - 양수 확인', () {
        expect(CurrencyUtils.isPositive(1), isTrue);
        expect(CurrencyUtils.isPositive(1000), isTrue);
        expect(CurrencyUtils.isPositive(0), isFalse);
        expect(CurrencyUtils.isPositive(-1), isFalse);
      });

      test('isNegative - 음수 확인', () {
        expect(CurrencyUtils.isNegative(-1), isTrue);
        expect(CurrencyUtils.isNegative(-1000), isTrue);
        expect(CurrencyUtils.isNegative(0), isFalse);
        expect(CurrencyUtils.isNegative(1), isFalse);
      });

      test('isZero - 0 확인', () {
        expect(CurrencyUtils.isZero(0), isTrue);
        expect(CurrencyUtils.isZero(1), isFalse);
        expect(CurrencyUtils.isZero(-1), isFalse);
      });

      test('isInRange - 범위 확인', () {
        expect(CurrencyUtils.isInRange(5000, 1000, 10000), isTrue);
        expect(CurrencyUtils.isInRange(1000, 1000, 10000), isTrue);
        expect(CurrencyUtils.isInRange(10000, 1000, 10000), isTrue);
        expect(CurrencyUtils.isInRange(500, 1000, 10000), isFalse);
        expect(CurrencyUtils.isInRange(15000, 1000, 10000), isFalse);
      });

      test('isAboveMinimum - 최소값 이상 확인', () {
        expect(CurrencyUtils.isAboveMinimum(1000, 500), isTrue);
        expect(CurrencyUtils.isAboveMinimum(500, 500), isTrue);
        expect(CurrencyUtils.isAboveMinimum(400, 500), isFalse);
      });

      test('isBelowMaximum - 최대값 이하 확인', () {
        expect(CurrencyUtils.isBelowMaximum(500, 1000), isTrue);
        expect(CurrencyUtils.isBelowMaximum(1000, 1000), isTrue);
        expect(CurrencyUtils.isBelowMaximum(1500, 1000), isFalse);
      });
    });

    group('계산 메서드 테스트', () {
      test('abs - 절대값', () {
        expect(CurrencyUtils.abs(1000), equals(1000));
        expect(CurrencyUtils.abs(-1000), equals(1000));
        expect(CurrencyUtils.abs(0), equals(0));
      });

      test('add - 덧셈', () {
        expect(CurrencyUtils.add(1000, 2000), equals(3000));
        expect(CurrencyUtils.add(-1000, 2000), equals(1000));
        expect(CurrencyUtils.add(0, 1000), equals(1000));
      });

      test('subtract - 뺄셈', () {
        expect(CurrencyUtils.subtract(2000, 1000), equals(1000));
        expect(CurrencyUtils.subtract(1000, 2000), equals(-1000));
        expect(CurrencyUtils.subtract(1000, 0), equals(1000));
      });

      test('applyPercentage - 퍼센트 적용', () {
        expect(CurrencyUtils.applyPercentage(1000, 10), equals(100));
        expect(CurrencyUtils.applyPercentage(1000, 50), equals(500));
        expect(CurrencyUtils.applyPercentage(1234, 15), equals(185)); // 반올림
        expect(CurrencyUtils.applyPercentage(1000, 0), equals(0));
      });

      test('roundToThousand - 천원 단위 반올림', () {
        expect(CurrencyUtils.roundToThousand(1234), equals(1000));
        expect(CurrencyUtils.roundToThousand(1500), equals(2000));
        expect(CurrencyUtils.roundToThousand(1499), equals(1000));
        expect(CurrencyUtils.roundToThousand(2600), equals(3000));
        expect(CurrencyUtils.roundToThousand(0), equals(0));
      });

      test('roundToTenThousand - 만원 단위 반올림', () {
        expect(CurrencyUtils.roundToTenThousand(12345), equals(10000));
        expect(CurrencyUtils.roundToTenThousand(15000), equals(20000));
        expect(CurrencyUtils.roundToTenThousand(14999), equals(10000));
        expect(CurrencyUtils.roundToTenThousand(25000), equals(30000));
        expect(CurrencyUtils.roundToTenThousand(0), equals(0));
      });
    });

    group('반올림 메서드 테스트', () {
      test('roundToThousand - 천원 단위 반올림', () {
        expect(CurrencyUtils.roundToThousand(1234), equals(1000));
        expect(CurrencyUtils.roundToThousand(1500), equals(2000));
        expect(CurrencyUtils.roundToThousand(1499), equals(1000));
        expect(CurrencyUtils.roundToThousand(2600), equals(3000));
        expect(CurrencyUtils.roundToThousand(0), equals(0));
      });

      test('roundToTenThousand - 만원 단위 반올림', () {
        expect(CurrencyUtils.roundToTenThousand(12345), equals(10000));
        expect(CurrencyUtils.roundToTenThousand(15000), equals(20000));
        expect(CurrencyUtils.roundToTenThousand(14999), equals(10000));
        expect(CurrencyUtils.roundToTenThousand(25000), equals(30000));
        expect(CurrencyUtils.roundToTenThousand(0), equals(0));
      });
    });

    group('배열 계산 메서드 테스트', () {
      test('sum - 합계', () {
        expect(CurrencyUtils.sum([1000, 2000, 3000]), equals(6000));
        expect(CurrencyUtils.sum([100, -50, 200]), equals(250));
        expect(CurrencyUtils.sum([]), equals(0));
        expect(CurrencyUtils.sum([1000]), equals(1000));
      });

      test('average - 평균', () {
        expect(CurrencyUtils.average([1000, 2000, 3000]), equals(2000.0));
        expect(CurrencyUtils.average([100, 200]), equals(150.0));
        expect(CurrencyUtils.average([]), equals(0.0));
        expect(CurrencyUtils.average([1000]), equals(1000.0));
        expect(CurrencyUtils.average([1000, 1500]), equals(1250.0));
      });

      test('max - 최대값', () {
        expect(CurrencyUtils.max([1000, 2000, 3000]), equals(3000));
        expect(CurrencyUtils.max([100, -50, 200]), equals(200));
        expect(CurrencyUtils.max([]), equals(0));
        expect(CurrencyUtils.max([1000]), equals(1000));
        expect(CurrencyUtils.max([-100, -200, -50]), equals(-50));
      });

      test('min - 최소값', () {
        expect(CurrencyUtils.min([1000, 2000, 3000]), equals(1000));
        expect(CurrencyUtils.min([100, -50, 200]), equals(-50));
        expect(CurrencyUtils.min([]), equals(0));
        expect(CurrencyUtils.min([1000]), equals(1000));
        expect(CurrencyUtils.min([-100, -200, -50]), equals(-200));
      });
    });

    group('UI 헬퍼 메서드 테스트', () {
      test('getAmountColorHex - 금액별 색상', () {
        expect(CurrencyUtils.getAmountColorHex(1000), equals('#2196F3')); // 파란색
        expect(
          CurrencyUtils.getAmountColorHex(-1000),
          equals('#F44336'),
        ); // 빨간색
        expect(CurrencyUtils.getAmountColorHex(0), equals('#9E9E9E')); // 회색
      });

      test('getAmountSign - 금액 부호', () {
        expect(CurrencyUtils.getAmountSign(1000), equals('+'));
        expect(CurrencyUtils.getAmountSign(-1000), equals('-'));
        expect(CurrencyUtils.getAmountSign(0), equals(''));
      });
    });

    group('극단적인 케이스 테스트', () {
      test('매우 큰 숫자 처리', () {
        const largeNumber = 999999999;
        expect(
          CurrencyUtils.formatCurrency(largeNumber),
          equals('999,999,999'),
        );
        expect(
          CurrencyUtils.formatCurrencyWithWon(largeNumber),
          equals('999,999,999원'),
        );
        expect(CurrencyUtils.isPositive(largeNumber), isTrue);
      });

      test('매우 작은 음수 처리', () {
        const smallNumber = -999999999;
        expect(
          CurrencyUtils.formatCurrency(smallNumber),
          equals('-999,999,999'),
        );
        expect(CurrencyUtils.isNegative(smallNumber), isTrue);
        expect(CurrencyUtils.abs(smallNumber), equals(999999999));
      });

      test('경계값 테스트', () {
        expect(CurrencyUtils.isInRange(1000, 1000, 1000), isTrue);
        expect(CurrencyUtils.applyPercentage(1, 100), equals(1));
        expect(CurrencyUtils.roundToThousand(1), equals(0));
      });
    });

    group('실제 사용 시나리오 테스트', () {
      test('상품 가격 포맷팅 시나리오', () {
        const productPrice = 15000;
        expect(
          CurrencyUtils.formatCurrencyWithWon(productPrice),
          equals('15,000원'),
        );
        expect(
          CurrencyUtils.formatSimpleAmount(productPrice),
          equals('1만 5,000원'),
        );
      });

      test('매출 합계 계산 시나리오', () {
        final dailySales = [15000, 23000, 18000, 31000, 12000];

        final totalSales = CurrencyUtils.sum(dailySales);
        final averageSale = CurrencyUtils.average(dailySales);
        final maxSale = CurrencyUtils.max(dailySales);
        final minSale = CurrencyUtils.min(dailySales);

        expect(totalSales, equals(99000));
        expect(averageSale, equals(19800.0));
        expect(maxSale, equals(31000));
        expect(minSale, equals(12000));
      });

      test('가격 범위 검증 시나리오', () {
        const minPrice = 1000;
        const maxPrice = 100000;

        expect(CurrencyUtils.isInRange(500, minPrice, maxPrice), isFalse);
        expect(CurrencyUtils.isInRange(50000, minPrice, maxPrice), isTrue);
        expect(CurrencyUtils.isInRange(150000, minPrice, maxPrice), isFalse);
      });
    });
  });
}
