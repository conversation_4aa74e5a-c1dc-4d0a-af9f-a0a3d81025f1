import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:io';
import '../utils/image_sync_utils.dart';
import '../utils/image_cache.dart';
import '../widgets/skeleton_loading.dart';

/// 상품 이미지를 최적화하여 표시하는 공통 위젯입니다.
/// - 로컬 파일 우선, 없으면 네트워크 이미지 사용
/// - 캐싱 최적화로 성능 향상 및 깜빡임 방지
/// - 스켈레톤 로딩으로 더 나은 UX 제공
/// - 재사용성, UX 일관성, 접근성 향상 목적
class ProductImage extends StatefulWidget {
  final String imagePath;
  final BoxFit? fit;
  final double? width;
  final double? height;

  const ProductImage({
    super.key,
    required this.imagePath,
    this.fit,
    this.width,
    this.height,
  });

  @override
  State<ProductImage> createState() => _ProductImageState();
}

class _ProductImageState extends State<ProductImage> with AutomaticKeepAliveClientMixin {
  // 앱 전체에서 공유되는 정적 캐시 - 깜빡거림 완전 방지
  static final Map<String, Widget> _globalImageCache = {};
  static final Set<String> _currentlyLoading = {};

  Widget? _imageWidget;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  bool get wantKeepAlive => true; // 이미지 위젯이 dispose되지 않도록 유지

  @override
  void initState() {
    super.initState();
    _loadImageWithCache();
  }

  @override
  void didUpdateWidget(ProductImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imagePath != widget.imagePath) {
      _loadImageWithCache();
    }
  }

  /// 캐시를 사용한 이미지 로딩 처리 - 깜빡거림 완전 방지
  Future<void> _loadImageWithCache() async {
    if (!mounted) return;

    // 1. 캐시에 있으면 즉시 사용 (깜빡거림 없음)
    if (_globalImageCache.containsKey(widget.imagePath)) {
      setState(() {
        _imageWidget = _globalImageCache[widget.imagePath];
        _isLoading = false;
        _hasError = false;
      });
      return;
    }

    // 2. 이미 로딩 중이면 완료까지 대기
    if (_currentlyLoading.contains(widget.imagePath)) {
      await _waitForLoading();
      return;
    }

    // 3. 새로 로딩 시작
    _currentlyLoading.add(widget.imagePath);

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final imageWidget = await _determineImageWidget();

      // 캐시에 저장
      _globalImageCache[widget.imagePath] = imageWidget;

      if (mounted) {
        setState(() {
          _imageWidget = imageWidget;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _isLoading = false;
        });
      }
    } finally {
      _currentlyLoading.remove(widget.imagePath);
    }
  }

  /// 로딩 완료까지 대기
  Future<void> _waitForLoading() async {
    while (_currentlyLoading.contains(widget.imagePath)) {
      await Future.delayed(const Duration(milliseconds: 50));
      if (!mounted) return;
    }

    // 로딩 완료 후 캐시에서 가져오기
    if (_globalImageCache.containsKey(widget.imagePath)) {
      setState(() {
        _imageWidget = _globalImageCache[widget.imagePath];
        _isLoading = false;
        _hasError = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // AutomaticKeepAliveClientMixin 필수 호출

    // 성능 최적화를 위한 RepaintBoundary 적용
    return RepaintBoundary(
      child: _isLoading
          ? _buildSkeletonLoader()
          : _hasError
              ? _buildErrorWidget()
              : _imageWidget ?? _buildErrorWidget(),
    );
  }

  /// 로컬 파일 우선, 없으면 네트워크 이미지 사용하는 위젯 결정
  Future<Widget> _determineImageWidget() async {
    // 1. 로컬 파일인지 확인 (캐시 우선)
    if (ImageSyncUtils.isLocalImagePath(widget.imagePath)) {
      final localExists = await ImageStateCache.checkFileExists(widget.imagePath);
      if (localExists) {
        return _buildLocalImage();
      }
    }

    // 2. 네트워크 URL인지 확인
    if (ImageSyncUtils.isNetworkImagePath(widget.imagePath)) {
      return _buildNetworkImage();
    }

    // 3. 둘 다 아니면 에러
    return _buildErrorWidget();
  }

  /// 로컬 이미지 위젯 생성
  Widget _buildLocalImage() {
    return Image.file(
      File(widget.imagePath),
      fit: widget.fit ?? BoxFit.cover,
      width: widget.width,
      height: widget.height,
      filterQuality: FilterQuality.medium,
      cacheWidth: (widget.width?.toInt() ?? 400).clamp(200, 600),
      cacheHeight: (widget.height?.toInt() ?? 400).clamp(200, 600),
      errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
    );
  }

  /// 네트워크 이미지 위젯 생성
  Widget _buildNetworkImage() {
    return CachedNetworkImage(
      imageUrl: widget.imagePath,
      fit: widget.fit ?? BoxFit.cover,
      width: widget.width,
      height: widget.height,
      placeholder: (context, url) => _buildSkeletonLoader(),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      memCacheWidth: (widget.width?.toInt() ?? 400).clamp(200, 600),
      memCacheHeight: (widget.height?.toInt() ?? 400).clamp(200, 600),
      maxWidthDiskCache: 400,
      maxHeightDiskCache: 400,
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 100),
      useOldImageOnUrlChange: true,
      cacheKey: widget.imagePath.hashCode.toString(),
    );
  }

  /// 스켈레톤 로더 위젯
  Widget _buildSkeletonLoader() {
    return SkeletonLoading.image(
      width: widget.width,
      height: widget.height,
      borderRadius: BorderRadius.circular(8),
    );
  }

  /// 에러 위젯 (기본 상품 이미지)
  Widget _buildErrorWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.white, // 흰색 배경, 테두리 제거
      child: Center(
        child: Icon(
          Icons.inventory_2_outlined,
          size: (widget.width != null && widget.height != null) 
            ? (widget.width! * 0.3).clamp(24.0, 40.0)
            : 28.0,
          color: Colors.grey[400],
        ),
      ),
    );
  }
}
