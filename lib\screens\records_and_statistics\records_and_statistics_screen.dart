import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/sales_log_provider.dart';
import '../../providers/seller_provider.dart';
import '../sales_log/sales_log_list_tab.dart';
import 'statistics_tab_content.dart';

import '../../widgets/unified_filter_dialog.dart';

/// 기록&통계 화면
/// 
/// 판매 기록과 통계를 하나의 화면에서 관리하는 2탭 구조의 화면입니다.
/// - 좌측 탭: 판매 기록 (기존 SalesLogListTab 기반)
/// - 우측 탭: 통계 (기존 StatisticsScreen 기반)
/// - 통합된 필터 다이얼로그 제공
/// - 슬라이드 탭 전환 지원
class RecordsAndStatisticsScreen extends ConsumerStatefulWidget {
  const RecordsAndStatisticsScreen({super.key});

  @override
  ConsumerState<RecordsAndStatisticsScreen> createState() => _RecordsAndStatisticsScreenState();
}

class _RecordsAndStatisticsScreenState extends ConsumerState<RecordsAndStatisticsScreen>
    with SingleTickerProviderStateMixin, RestorationMixin {
  late TabController _tabController;

  // 필터 상태
  String _selectedSeller = '전체 판매자';
  DateTimeRange? _selectedDateRange;

  @override
  String? get restorationId => 'records_and_statistics_screen';
  
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 데이터 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
      ref.read(sellerNotifierProvider.notifier).loadSellers();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 화면이 다시 포커스될 때 데이터 새로고침
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '기록&통계',
          style: Theme.of(context).textTheme.titleLarge!.copyWith(
            fontFamily: 'Pretendard',
            color: Theme.of(context).colorScheme.onPrimary,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          // 통합 필터 버튼
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showUnifiedFilterDialog,
            tooltip: '필터 설정',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: '판매 기록'),
            Tab(text: '통계'),
          ],
        ),
      ),
      body: SafeArea(
        child: TabBarView(
          controller: _tabController,
          children: [
            // 판매 기록 탭
            SalesLogListTab(
              selectedSeller: _selectedSeller,
              selectedDateRange: _selectedDateRange,
            ),
            // 통계 탭 (새로운 StatisticsTabContent 사용)
            StatisticsTabContent(
              selectedSeller: _selectedSeller,
              selectedDateRange: _selectedDateRange,
            ),
          ],
        ),
      ),
    );
  }

  /// 통합 필터 다이얼로그 표시
  Future<void> _showUnifiedFilterDialog() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => UnifiedFilterDialog(
        initialSeller: _selectedSeller,
        initialDateRange: _selectedDateRange,
      ),
    );

    if (result != null) {
      setState(() {
        _selectedSeller = result['seller'] ?? '전체 판매자';
        _selectedDateRange = result['dateRange'];
      });
    }
  }
}


