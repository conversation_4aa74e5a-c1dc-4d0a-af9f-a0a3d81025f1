import 'package:flutter/material.dart';

/// 진행 상태를 표시하는 위젯
class ProgressDisplay extends StatelessWidget {
  final double progress;
  final String? message;
  final String? subMessage;
  final bool showPercentage;
  final bool isIndeterminate;
  final VoidCallback? onCancel;
  final Color? color;
  final double strokeWidth;

  const ProgressDisplay({
    super.key,
    this.progress = 0.0,
    this.message,
    this.subMessage,
    this.showPercentage = true,
    this.isIndeterminate = false,
    this.onCancel,
    this.color,
    this.strokeWidth = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = color ?? theme.colorScheme.primary;

    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 300),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.1),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (message != null) ...[
              Text(
                message!,
                style: theme.textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
            ],
            Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: 80,
                  height: 80,
                  child: CircularProgressIndicator(
                    value: isIndeterminate ? null : progress,
                    strokeWidth: strokeWidth,
                    valueColor: AlwaysStoppedAnimation<Color>(effectiveColor),
                  ),
                ),
                if (showPercentage && !isIndeterminate)
                  Text(
                    '${(progress * 100).toInt()}%',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: effectiveColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
            if (subMessage != null) ...[
              const SizedBox(height: 16),
              Text(
                subMessage!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.textTheme.bodySmall?.color?.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (onCancel != null) ...[
              const SizedBox(height: 16),
              TextButton(onPressed: onCancel, child: const Text('취소')),
            ],
          ],
        ),
      ),
    );
  }
}

/// 선형 진행 상태를 표시하는 위젯
class LinearProgressDisplay extends StatelessWidget {
  final double progress;
  final String? message;
  final String? subMessage;
  final bool showPercentage;
  final bool isIndeterminate;
  final VoidCallback? onCancel;
  final Color? color;
  final double height;

  const LinearProgressDisplay({
    super.key,
    this.progress = 0.0,
    this.message,
    this.subMessage,
    this.showPercentage = true,
    this.isIndeterminate = false,
    this.onCancel,
    this.color,
    this.height = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = color ?? theme.colorScheme.primary;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (message != null) ...[
            Text(message!, style: theme.textTheme.titleSmall),
            const SizedBox(height: 8),
          ],
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: isIndeterminate ? null : progress,
                  minHeight: height,
                  valueColor: AlwaysStoppedAnimation<Color>(effectiveColor),
                ),
              ),
              if (showPercentage && !isIndeterminate) ...[
                const SizedBox(width: 16),
                Text(
                  '${(progress * 100).toInt()}%',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: effectiveColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ],
          ),
          if (subMessage != null) ...[
            const SizedBox(height: 8),
            Text(
              subMessage!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.textTheme.bodySmall?.color?.withValues(alpha: 0.7),
              ),
            ),
          ],
          if (onCancel != null) ...[
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: onCancel,
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: const Text('취소'),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// 진행 상태 오버레이를 표시하는 위젯
class ProgressOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? message;
  final String? subMessage;
  final double? progress;
  final bool showPercentage;
  final VoidCallback? onCancel;
  final Color? color;

  const ProgressOverlay({
    super.key,
    required this.child,
    required this.isLoading,
    this.message,
    this.subMessage,
    this.progress,
    this.showPercentage = true,
    this.onCancel,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black54,
            child: ProgressDisplay(
              progress: progress ?? 0,
              message: message,
              subMessage: subMessage,
              showPercentage: showPercentage && progress != null,
              isIndeterminate: progress == null,
              onCancel: onCancel,
              color: color,
            ),
          ),
      ],
    );
  }
}

/// 진행률(퍼센트/단계 등)을 표시하는 커스텀 인디케이터 위젯입니다.
/// - Linear/Circular/Step 등 다양한 형태 지원
/// - 재사용성, UX 일관성, 접근성 향상 목적
class CustomProgressIndicator extends StatelessWidget {
  final double progress;
  final String? message;
  final String? subMessage;
  final bool showPercentage;
  final bool isIndeterminate;
  final VoidCallback? onCancel;
  final Color? color;
  final ProgressType type;

  const CustomProgressIndicator({
    super.key,
    this.progress = 0.0,
    this.message,
    this.subMessage,
    this.showPercentage = true,
    this.isIndeterminate = false,
    this.onCancel,
    this.color,
    this.type = ProgressType.circular,
  });

  @override
  Widget build(BuildContext context) {
    switch (type) {
      case ProgressType.circular:
        return ProgressDisplay(
          progress: progress,
          message: message,
          subMessage: subMessage,
          showPercentage: showPercentage,
          isIndeterminate: isIndeterminate,
          onCancel: onCancel,
          color: color,
        );
      case ProgressType.linear:
        return LinearProgressDisplay(
          progress: progress,
          message: message,
          subMessage: subMessage,
          showPercentage: showPercentage,
          isIndeterminate: isIndeterminate,
          onCancel: onCancel,
          color: color,
        );
    }
  }
}

/// 진행 상태 타입
enum ProgressType { circular, linear }

// [안내] 이 파일의 progress 파라미터는 ToastUtils.showProgress의 progressNotifier(ValueNotifier<double>)와는 별개로, 단순 double 값만 받는 구조입니다. ToastUtils 구조와 혼동하지 마세요.
