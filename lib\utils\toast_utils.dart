import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'logger_utils.dart';

/// 토스트/스낵바/피드백 메시지 표시를 지원하는 유틸리티 클래스입니다.
/// - 성공/오류/정보/경고/커스텀 메시지, 액션 버튼, 아이콘 등 지원
/// - riverpod 3.x Provider/Repository와 연동, 사용자 피드백/UX 개선 목적
/// - 상단 오버레이 기반으로 FAB와 하단 네비게이션을 방해하지 않는 토스트 메시지 표시
class ToastUtils {
  static OverlayEntry? _currentOverlay;
  /// 성공 메시지 표시
  static void showSuccess(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showTopToast(
      context,
      message,
      backgroundColor: AppColors.surface, // 연한 회색 배경
      foregroundColor: AppColors.success, // 성공 색상으로 텍스트
      icon: Icons.check_circle_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 에러 메시지 표시
  static void showError(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showTopToast(
      context,
      message,
      backgroundColor: AppColors.surface, // 연한 회색 배경
      foregroundColor: AppColors.error, // 에러 색상으로 텍스트
      icon: Icons.error_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 정보 메시지 표시
  static void showInfo(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showTopToast(
      context,
      message,
      backgroundColor: AppColors.surface, // 연한 회색 배경
      foregroundColor: AppColors.info, // 정보 색상으로 텍스트
      icon: Icons.info_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 경고 메시지 표시
  static void showWarning(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showTopToast(
      context,
      message,
      backgroundColor: AppColors.surface, // 연한 회색 배경
      foregroundColor: AppColors.warning, // 경고 색상으로 텍스트
      icon: Icons.warning_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 일반 메시지 표시
  static void showMessage(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showTopToast(
      context,
      message,
      backgroundColor: AppColors.surface, // 연한 회색 배경
      foregroundColor: AppColors.onSurface, // 기본 텍스트 색상
      icon: Icons.message_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 현재 표시 중인 토스트 메시지 해제
  static void dismiss(BuildContext context) {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }

  /// 기존 호환성을 위한 일반 토스트 메서드
  static void showToast(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    _showTopToast(
      context,
      message,
      backgroundColor: AppColors.surface, // 연한 회색 배경
      foregroundColor: AppColors.onSurface, // 기본 텍스트 색상
      icon: Icons.message_rounded,
      duration: duration,
    );
  }

  /// 기존 호환성을 위한 Duration 상수들
  static const Duration shortDuration = Duration(seconds: 2);
  static const Duration longDuration = Duration(seconds: 3);

  /// 상단 오버레이 토스트 표시 (FAB와 하단 네비게이션 방해 안함)
  static void _showTopToast(
    BuildContext context,
    String message, {
    IconData? icon,
    Color? backgroundColor,
    Color? foregroundColor,
    Duration? duration,
    String? actionLabel,
    VoidCallback? onAction,
    bool? dismissible,
  }) {
    try {
      // context 유효성 체크
      if (!context.mounted) return;

      // 기존 오버레이 제거
      _currentOverlay?.remove();
      _currentOverlay = null;

      // 오버레이 엔트리 생성
      _currentOverlay = OverlayEntry(
        builder: (context) => _TopToastWidget(
          message: message,
          icon: icon,
          backgroundColor: backgroundColor ?? AppColors.surface, // 연한 회색 배경
          foregroundColor: foregroundColor ?? AppColors.onSurface, // 적절한 텍스트 색상
          actionLabel: actionLabel,
          onAction: onAction,
          onDismiss: () {
            _currentOverlay?.remove();
            _currentOverlay = null;
          },
        ),
      );

      // 오버레이에 추가
      Overlay.of(context).insert(_currentOverlay!);

      // 자동 제거 타이머
      Future.delayed(duration ?? const Duration(seconds: 3), () {
        _currentOverlay?.remove();
        _currentOverlay = null;
      });

    } catch (e) {
      // 에러가 발생해도 앱이 크래시되지 않도록 처리
      LoggerUtils.logError('ToastUtils._showTopToast error: $e', tag: 'ToastUtils', error: e);
    }
  }
}

/// 상단 토스트 위젯
class _TopToastWidget extends StatefulWidget {
  final String message;
  final IconData? icon;
  final Color backgroundColor;
  final Color foregroundColor;
  final String? actionLabel;
  final VoidCallback? onAction;
  final VoidCallback onDismiss;

  const _TopToastWidget({
    required this.message,
    this.icon,
    required this.backgroundColor,
    required this.foregroundColor,
    this.actionLabel,
    this.onAction,
    required this.onDismiss,
  });

  @override
  State<_TopToastWidget> createState() => _TopToastWidgetState();
}

class _TopToastWidgetState extends State<_TopToastWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenHeight = mediaQuery.size.height;
    final screenWidth = mediaQuery.size.width;
    final bottomPadding = mediaQuery.padding.bottom;
    final isLandscape = screenWidth > screenHeight;

    // POS 버튼 위치 계산 (FloatingActionButton + BottomNavigationBar 고려)
    // 하단 네비게이션 바 높이: 약 56-80px
    // FloatingActionButton 크기: 약 56px
    // 여백: 16px
    final bottomNavHeight = isLandscape ? 56.0 : 80.0; // 가로모드에서는 더 작게
    final fabSize = 56.0;
    final fabMargin = 16.0;
    final toastMargin = 20.0; // POS 버튼 위 여백

    // POS 버튼보다 살짝 위 위치 계산
    final posButtonTop = screenHeight - bottomPadding - bottomNavHeight - fabSize - fabMargin;
    final toastPosition = posButtonTop - toastMargin - 60; // 토스트 높이 고려 (약 60px)

    return Positioned(
      top: toastPosition,
      left: 16,
      right: 16,
      child: SlideTransition(
        position: _slideAnimation,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.primarySeed, // 웜 테라코타 테두리
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color: widget.foregroundColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                  ],
                  Expanded(
                    child: Text(
                      widget.message,
                      style: TextStyle(
                        color: widget.foregroundColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  if (widget.actionLabel != null && widget.onAction != null) ...[
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: () {
                        widget.onAction?.call();
                        widget.onDismiss();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: widget.foregroundColor,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                      ),
                      child: Text(widget.actionLabel!),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
