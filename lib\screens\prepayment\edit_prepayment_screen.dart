import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/prepayment.dart';

class EditPrepaymentScreen extends ConsumerStatefulWidget {
  final Prepayment prepayment;
  const EditPrepaymentScreen({super.key, required this.prepayment});

  @override
  ConsumerState<EditPrepaymentScreen> createState() =>
      _EditPrepaymentScreenState();
}

class _EditPrepaymentScreenState extends ConsumerState<EditPrepaymentScreen>
    with RestorationMixin {
  @override
  String? get restorationId => 'edit_prepayment_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('${widget.prepayment.buyerName} 선입금 수정')),
      body: Center(
        child: Text('${widget.prepayment.buyerName} 선입금 수정 화면 (구현 예정)'),
      ),
    );
  }
}
