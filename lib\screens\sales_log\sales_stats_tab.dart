import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/sales_log.dart';
import '../../models/product_sale_stat.dart';
import '../../models/transaction_type.dart';
import '../../providers/sales_log_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/category_provider.dart';
import '../../utils/currency_utils.dart';

class SalesStatsTab extends ConsumerWidget {
  final String selectedSeller;
  final DateTimeRange? selectedDateRange;

  const SalesStatsTab({
    super.key,
    required this.selectedSeller,
    this.selectedDateRange,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final salesLogState = ref.watch(salesLogNotifierProvider);

    if (salesLogState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final filteredLogs = _getFilteredSalesLogs(ref.watch(salesLogsProvider));
    final productStats = _calculateProductStats(filteredLogs, ref);

    if (productStats.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      itemCount: productStats.length,
      itemExtent: 60.0, // 고정 높이로 스크롤 성능 최적화
      itemBuilder: (context, index) {
        final stat = productStats[index];
        return _buildProductStatItem(context, stat);
      },
      restorationId: 'sales_stats_tab_scroll',
    );
  }

  /// 필터링된 판매 기록 목록 (SALE 거래만)
  List<SalesLog> _getFilteredSalesLogs(List<SalesLog> allLogs) {
    return allLogs.where((log) {
      // SALE 거래만 포함
      if (log.transactionType != TransactionType.sale) {
        return false;
      }

      // 판매자 필터
      if (selectedSeller != '전체 판매자' &&
          (log.sellerName ?? '알 수 없음') != selectedSeller) {
        return false;
      }

      // 날짜 범위 필터
      if (selectedDateRange != null) {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
        final startDate = selectedDateRange!.start;
        final endDate = selectedDateRange!.end.add(
          const Duration(days: 1),
        ); // 종료일 포함

        if (logDate.isBefore(startDate) || logDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// 상품별 판매 통계 계산
  List<ProductSaleStat> _calculateProductStats(List<SalesLog> logs, WidgetRef ref) {
    final statsMap = <String, ProductSaleStat>{};

    for (final log in logs) {
      final productName = _buildProductDisplayName(log, ref);
      final quantity = log.soldQuantity;
      final revenue = log.totalAmount;

      if (statsMap.containsKey(productName)) {
        final currentStat = statsMap[productName]!;
        statsMap[productName] = ProductSaleStat(
          productName: productName,
          totalQuantitySold: currentStat.totalQuantitySold + quantity,
          totalRevenue: currentStat.totalRevenue + revenue,
        );
      } else {
        statsMap[productName] = ProductSaleStat(
          productName: productName,
          totalQuantitySold: quantity,
          totalRevenue: revenue,
        );
      }
    }

    // 매출액 기준 내림차순 정렬
    final sortedStats = statsMap.values.toList()
      ..sort((a, b) => b.totalRevenue.compareTo(a.totalRevenue));

    return sortedStats;
  }

  /// 빈 상태 위젯
  Widget _buildEmptyState() {
    String message = '판매 통계를 표시할 데이터가 없습니다.';

    if (selectedSeller != '전체 판매자') {
      message = '$selectedSeller의 판매 통계 데이터가 없습니다.';
    }

    if (selectedDateRange != null) {
      message = '선택한 기간의 판매 통계 데이터가 없습니다.';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.bar_chart_outlined, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(fontFamily: 'Pretendard', fontSize: 18, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 상품별 통계 아이템 위젯 (원본과 동일한 구조)
  Widget _buildProductStatItem(BuildContext context, ProductSaleStat stat) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 상품명 (가중치 3)
          Expanded(
            flex: 3,
            child: Text(
              stat.productName,
              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                fontFamily: 'Pretendard',
                fontSize: 16,
                fontWeight: FontWeight.normal,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // 판매 개수 (가중치 1, 우측 정렬)
          Expanded(
            flex: 1,
            child: Text(
              '${stat.totalQuantitySold}개',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.end,
            ),
          ),

          // 총 판매액 (가중치 2, 우측 정렬)
          Expanded(
            flex: 2,
            child: Text(
              CurrencyUtils.formatCurrency(stat.totalRevenue),
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  /// 상품명에 카테고리명을 포함하여 표시명 생성
  String _buildProductDisplayName(SalesLog log, WidgetRef ref) {
    try {
      // 카테고리 정보가 없거나 productId가 없는 경우 원본 상품명 반환
      if (log.productId == null) {
        return log.productName;
      }

      // 상품 정보 가져오기
      final productsAsync = ref.read(productNotifierProvider);
      final categoriesAsync = ref.read(categoryNotifierProvider);

      if (!productsAsync.isLoading && !productsAsync.hasError &&
          categoriesAsync.hasValue) {
        final products = productsAsync.products;
        final categories = categoriesAsync.value!;

        // 해당 상품 찾기
        try {
          final product = products.firstWhere((p) => p.id == log.productId);

          // 해당 카테고리 찾기
          try {
            final category = categories.firstWhere(
              (cat) => cat.id == product.categoryId,
            );
            return '[${category.name}]${log.productName}';
          } catch (e) {
            // 카테고리를 찾을 수 없는 경우 원본 상품명 반환
            return log.productName;
          }
        } catch (e) {
          // 상품을 찾을 수 없는 경우 원본 상품명 반환
          return log.productName;
        }
      }
    } catch (e) {
      // 에러 발생 시 원본 상품명 반환
    }

    // 기본적으로 상품명만 반환
    return log.productName;
  }
}

