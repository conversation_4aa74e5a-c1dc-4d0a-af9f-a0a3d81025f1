import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 입력 폼의 검증 상태를 나타내는 enum
enum ValidationState {
  /// 초기 상태
  initial,

  /// 입력중
  typing,

  /// 검증 성공
  valid,

  /// 검증 실패
  invalid,
}

/// 폼 입력값 유효성 검증 결과(피드백)를 표시하는 공통 위젯입니다.
/// - 에러/경고/성공 메시지, 아이콘, 커스텀 스타일 등 지원
/// - 재사용성, UX 일관성, 접근성 향상 목적
class FormValidationFeedback extends ConsumerWidget {
  const FormValidationFeedback({
    super.key,
    required this.child,
    required this.validationState,
    this.errorMessage,
    this.helperText,
    this.onRetry,
  });

  /// 감싸질 TextFormField
  final Widget child;

  /// 현재 검증 상태
  final ValidationState validationState;

  /// 에러 메시지 (validationState가 invalid일 때만 표시)
  final String? errorMessage;

  /// 도움말 텍스트 (validationState가 initial 또는 typing일 때만 표시)
  final String? helperText;

  /// 재시도 콜백 (validationState가 invalid일 때만 활성화)
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        child,
        if (validationState == ValidationState.invalid && errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(top: 8.0, left: 12.0),
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: Theme.of(context).colorScheme.error,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    errorMessage!,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                      fontSize: 12,
                    ),
                  ),
                ),
                if (onRetry != null)
                  TextButton(
                    onPressed: onRetry,
                    style: TextButton.styleFrom(
                      minimumSize: const Size(60, 24),
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                    ),
                    child: const Text('재시도'),
                  ),
              ],
            ),
          ),
        if ((validationState == ValidationState.initial ||
                validationState == ValidationState.typing) &&
            helperText != null)
          Padding(
            padding: const EdgeInsets.only(top: 8.0, left: 12.0),
            child: Text(
              helperText!,
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }
}

class FormValidationController extends StateNotifier<ValidationState> {
  FormValidationController() : super(ValidationState.initial);

  /// 입력 시작
  void startTyping() {
    state = ValidationState.typing;
  }

  /// 검증 성공
  void setValid() {
    state = ValidationState.valid;
  }

  /// 검증 실패
  void setInvalid() {
    state = ValidationState.invalid;
  }

  /// 초기 상태로 리셋
  void reset() {
    state = ValidationState.initial;
  }
}

final formValidationControllerProvider = StateNotifierProvider<FormValidationController, ValidationState>((ref) {
  return FormValidationController();
});

class FormValidationControllerFamily extends StateNotifier<ValidationState> {
  final String id;

  FormValidationControllerFamily(this.id) : super(ValidationState.initial);

  /// 입력 시작
  void startTyping() {
    state = ValidationState.typing;
  }

  /// 검증 성공
  void setValid() {
    state = ValidationState.valid;
  }

  /// 검증 실패
  void setInvalid() {
    state = ValidationState.invalid;
  }

  /// 초기 상태로 리셋
  void reset() {
    state = ValidationState.initial;
  }
}

final formValidationControllerFamilyProvider = StateNotifierProvider.family<FormValidationControllerFamily, ValidationState, String>((ref, id) {
  return FormValidationControllerFamily(id);
});
