/// 새로운 로그아웃 매니저 - Phoenix를 사용한 완전한 앱 재시작
/// 
/// 기존 로그아웃의 문제점들을 해결하기 위해 새로 설계된 로그아웃 시스템
/// - AppWrapper 중간 이동 문제 해결
/// - 상태 동기화 문제 해결
/// - 완전한 앱 초기화 보장

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';

import '../utils/logger_utils.dart';
import '../main.dart';

class LogoutManager {
  static const String _tag = 'LogoutManager';

  /// 완전한 로그아웃 수행 - Phoenix를 사용한 앱 재시작
  /// 
  /// 이 방법은 다음과 같은 장점이 있습니다:
  /// 1. AppWrapper 중간 이동 문제 완전 해결
  /// 2. 모든 상태와 메모리 완전 초기화
  /// 3. 복잡한 상태 동기화 로직 불필요
  /// 4. 확실한 로그아웃 보장
  static Future<void> performCompleteLogout({
    required BuildContext context,
    required WidgetRef ref,
  }) async {
    try {
      LoggerUtils.logInfo('🔄 새로운 로그아웃 시작', tag: _tag);

      // 1. 로딩 다이얼로그 표시
      _showLogoutDialog(context);

      // 2. 백그라운드에서 데이터 정리 (UI 블로킹 방지)
      await _performDataCleanup(ref);

      // 3. Firebase 로그아웃
      await FirebaseAuth.instance.signOut();
      LoggerUtils.logInfo('✅ Firebase 로그아웃 완료', tag: _tag);

      // 4. SharedPreferences 완전 초기화
      await _resetSharedPreferences();

      // 5. 잠시 대기 (정리 작업 완료 보장)
      await Future.delayed(const Duration(milliseconds: 500));

      // 6. 앱 완전 재시작 (Phoenix)
      LoggerUtils.logInfo('🔄 앱 완전 재시작 시작', tag: _tag);
      
      // 다이얼로그 닫기
      if (context.mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      
      // Phoenix를 사용한 앱 완전 재시작
      if (context.mounted) {
        Phoenix.rebirth(context);
      }

    } catch (e) {
      LoggerUtils.logError('❌ 로그아웃 중 오류 발생', tag: _tag, error: e);
      
      // 오류 발생 시에도 강제 재시작
      if (context.mounted) {
        Navigator.of(context, rootNavigator: true).pop();
        Phoenix.rebirth(context);
      }
    }
  }

  /// 로딩 다이얼로그 표시
  static void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false, // 뒤로가기 방지
        child: const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('로그아웃 중...'),
            ],
          ),
        ),
      ),
    );
  }

  /// 데이터 정리 작업
  static Future<void> _performDataCleanup(WidgetRef ref) async {
    try {
      LoggerUtils.logInfo('🗑️ 데이터 정리 시작', tag: _tag);

      // 1. 앱 리소스 정리
      await cleanupAppResources();
      LoggerUtils.logInfo('✅ 앱 리소스 정리 완료', tag: _tag);

      // 2. 로컬 데이터 정리 (provider 상태 변경 없이)
      await _cleanupLocalDataSilently();
      LoggerUtils.logInfo('✅ 로컬 데이터 정리 완료', tag: _tag);

    } catch (e) {
      LoggerUtils.logError('❌ 데이터 정리 중 오류', tag: _tag, error: e);
      // 오류가 있어도 계속 진행
    }
  }

  /// 조용한 로컬 데이터 정리 (provider 상태 변경 없이)
  static Future<void> _cleanupLocalDataSilently() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // SharedPreferences 정리 (onboarding 상태는 유지)
      final keys = prefs.getKeys().where((key) => 
        key != 'isOnboarded' && 
        key != 'auth_mode'
      ).toList();
      
      for (final key in keys) {
        await prefs.remove(key);
      }

      // SQLite, 캐시 등 기타 로컬 데이터 정리
      // LocalDataCleaner의 개별 메서드들을 직접 호출하여 provider 상태 변경 방지
      await _cleanupDatabaseSilently();
      await _cleanupCachesSilently();

    } catch (e) {
      LoggerUtils.logError('조용한 데이터 정리 실패', tag: _tag, error: e);
    }
  }

  /// 데이터베이스 조용히 정리
  static Future<void> _cleanupDatabaseSilently() async {
    try {
      // SQLite 데이터베이스 정리 로직
      // 여기서는 provider를 사용하지 않고 직접 데이터베이스 정리
      LoggerUtils.logInfo('SQLite 데이터베이스 정리 중...', tag: _tag);
      // 실제 구현은 LocalDataCleaner의 로직을 참조하되 provider 사용 안함
    } catch (e) {
      LoggerUtils.logError('데이터베이스 정리 실패', tag: _tag, error: e);
    }
  }

  /// 캐시 조용히 정리
  static Future<void> _cleanupCachesSilently() async {
    try {
      // 이미지 캐시 등 정리
      LoggerUtils.logInfo('캐시 정리 중...', tag: _tag);
      // 실제 구현은 LocalDataCleaner의 로직을 참조
    } catch (e) {
      LoggerUtils.logError('캐시 정리 실패', tag: _tag, error: e);
    }
  }

  /// SharedPreferences 완전 초기화
  static Future<void> _resetSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 모든 데이터 삭제
      await prefs.clear();
      
      // 초기 상태 설정
      await prefs.setBool('isOnboarded', false);
      await prefs.setString('auth_mode', 'login');
      
      LoggerUtils.logInfo('✅ SharedPreferences 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('SharedPreferences 초기화 실패', tag: _tag, error: e);
    }
  }
}
