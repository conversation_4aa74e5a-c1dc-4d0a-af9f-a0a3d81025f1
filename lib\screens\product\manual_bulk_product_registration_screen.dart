import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';

import '../../models/product.dart';
import '../../providers/product_provider.dart';
import '../../providers/seller_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../providers/data_sync_provider.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../utils/image_utils.dart';
import '../../widgets/registration_complete_page.dart';
import '../../widgets/image_crop_widget.dart';

import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

/// 상품 입력 항목을 관리하는 클래스
class ProductEntry {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController quantityController = TextEditingController();
  String? selectedSeller;
  int? selectedCategoryId;
  String? originalPaddedImagePath; // 크롭을 위한 원본 패딩 이미지 경로
  String? croppedImagePath; // 크롭된 이미지 경로

  void dispose() {
    nameController.dispose();
    priceController.dispose();
    quantityController.dispose();
  }
}

/// 수동 대량 상품 등록 화면
/// 상품명부터 직접 입력하여 여러 상품을 한번에 등록
class ManualBulkProductRegistrationScreen extends ConsumerStatefulWidget {
  const ManualBulkProductRegistrationScreen({super.key});

  @override
  ConsumerState<ManualBulkProductRegistrationScreen> createState() => _ManualBulkProductRegistrationScreenState();
}

class _ManualBulkProductRegistrationScreenState extends ConsumerState<ManualBulkProductRegistrationScreen> {
  final List<ProductEntry> _productEntries = [];
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    // 기본적으로 3개의 빈 상품 항목으로 시작
    _addProductEntry();
    _addProductEntry();
    _addProductEntry();

    // 카테고리와 판매자 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(categoryNotifierProvider.notifier).loadCategories();
      _initializeDefaults();
    });
  }

  /// 기본값들을 초기화합니다.
  void _initializeDefaults() {
    // 대표 판매자 설정
    final sellerState = ref.read(sellerNotifierProvider);
    final defaultSeller = sellerState.defaultSeller;

    // 기본 카테고리 설정
    final categoriesAsync = ref.read(categoryNotifierProvider);
    int? defaultCategoryId;
    if (categoriesAsync.hasValue && categoriesAsync.value!.isNotEmpty) {
      final sortedCategories = [...categoriesAsync.value!]
        ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
      defaultCategoryId = sortedCategories.first.id;
    }

    // 모든 항목에 기본값 적용
    setState(() {
      for (final entry in _productEntries) {
        if (defaultSeller != null) {
          entry.selectedSeller = defaultSeller.name;
        }
        if (defaultCategoryId != null) {
          entry.selectedCategoryId = defaultCategoryId;
        }
      }
    });
  }

  @override
  void dispose() {
    // 모든 컨트롤러 정리
    for (final entry in _productEntries) {
      entry.dispose();
    }
    super.dispose();
  }

  void _addProductEntry() {
    final newEntry = ProductEntry();

    // 기본값 설정
    final sellerState = ref.read(sellerNotifierProvider);
    final defaultSeller = sellerState.defaultSeller;
    if (defaultSeller != null) {
      newEntry.selectedSeller = defaultSeller.name;
    }

    final categoriesAsync = ref.read(categoryNotifierProvider);
    if (categoriesAsync.hasValue && categoriesAsync.value!.isNotEmpty) {
      final sortedCategories = [...categoriesAsync.value!]
        ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
      newEntry.selectedCategoryId = sortedCategories.first.id;
    }

    setState(() {
      _productEntries.add(newEntry);
    });
  }

  /// 여러 개의 상품 항목을 한번에 추가합니다.
  void _addMultipleProductEntries(int count) {
    for (int i = 0; i < count; i++) {
      _addProductEntry();
    }
  }

  void _removeProductEntry(int index) {
    if (_productEntries.length > 1) {
      setState(() {
        _productEntries[index].dispose();
        _productEntries.removeAt(index);
      });
    }
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      appBar: AppBar(
        title: const Text('대량 상품 등록'),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          // 상품 추가 버튼
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddProductDialog,
            tooltip: '상품 추가',
          ),
          // 일괄 입력 버튼
          IconButton(
            icon: const Icon(Icons.auto_fix_high),
            onPressed: _showQuickInputDialog,
            tooltip: '일괄 입력',
          ),
          // 등록 버튼
          IconButton(
            icon: _isProcessing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.check),
            onPressed: _isProcessing ? null : _registerProducts,
            tooltip: '등록',
          ),
        ],
      ),
      body: SafeArea(
        child: _buildProductList(),
      ),
    );
  }

  /// 상품 추가 다이얼로그를 표시합니다.
  void _showAddProductDialog() {
    final countController = TextEditingController(text: '1');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('상품 추가'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('추가할 상품 개수를 입력하세요.'),
            const SizedBox(height: 16),
            TextField(
              controller: countController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: const InputDecoration(
                labelText: '개수',
                hintText: '1',
                suffixText: '개',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () {
              final count = int.tryParse(countController.text) ?? 1;
              if (count > 0 && count <= 50) { // 최대 50개 제한
                _addMultipleProductEntries(count);
                Navigator.of(context).pop();
              } else {
                ToastUtils.showError(context, '1~50개 사이의 숫자를 입력해주세요.');
              }
            },
            child: const Text('추가'),
          ),
        ],
      ),
    );
  }

  /// 상품 리스트를 구성합니다.
  Widget _buildProductList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _productEntries.length,
      itemBuilder: (context, index) {
        return _buildProductCard(index);
      },
    );
  }

  /// 개별 상품 카드를 구성합니다.
  Widget _buildProductCard(int index) {
    final entry = _productEntries[index];

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // 중앙정렬로 변경
          children: [
            // 왼쪽: 이미지와 상품 번호 (중앙정렬)
            Column(
              mainAxisAlignment: MainAxisAlignment.center, // 중앙정렬 추가
              children: [
                // 이미지 미리보기
                GestureDetector(
                  onTap: () => _selectImage(entry),
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey[50],
                    ),
                    child: entry.croppedImagePath != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              File(entry.croppedImagePath!),
                              fit: BoxFit.cover,
                            ),
                          )
                        : Icon(Icons.add_a_photo, color: Colors.grey[400], size: 24),
                  ),
                ),
                const SizedBox(height: 8),
                // 상품 번호
                Text(
                  '상품 ${index + 1}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(width: 12),

            // 오른쪽: 입력 필드들
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 상품명과 삭제 버튼
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: entry.nameController,
                          decoration: InputDecoration(
                            labelText: '상품명 *',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                            hintText: '상품명을 입력하세요',
                            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                            isDense: true,
                          ),
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      if (_productEntries.length > 1) ...[
                        const SizedBox(width: 8),
                        IconButton(
                          icon: const Icon(Icons.delete_outline, color: Colors.red, size: 20),
                          onPressed: () => _removeProductEntry(index),
                          tooltip: '삭제',
                          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                          padding: EdgeInsets.zero,
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 8),

                  // 가격과 수량 입력
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: entry.priceController,
                          decoration: InputDecoration(
                            labelText: '가격 *',
                            suffixText: '원',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                            isDense: true,
                          ),
                          style: const TextStyle(fontSize: 14),
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          controller: entry.quantityController,
                          decoration: InputDecoration(
                            labelText: '수량 *',
                            suffixText: '개',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                            isDense: true,
                          ),
                          style: const TextStyle(fontSize: 14),
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // 카테고리와 판매자 선택
                  Row(
                    children: [
                      Expanded(
                        child: _buildCategoryDropdown(entry),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildSellerDropdown(entry),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 카테고리 드롭다운을 구성합니다.
  Widget _buildCategoryDropdown(ProductEntry entry) {
    return Consumer(
      builder: (context, ref, child) {
        final categoriesAsync = ref.watch(categoryNotifierProvider);

        return categoriesAsync.when(
          loading: () => const SizedBox(
            height: 56,
            child: Center(child: CircularProgressIndicator()),
          ),
          error: (error, stack) => Container(
            height: 56,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(child: Text('오류')),
          ),
          data: (categories) {
            if (categories.isEmpty) {
              return Container(
                height: 56,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.red),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(child: Text('카테고리 없음')),
              );
            }

            return DropdownButtonFormField<int>(
              value: entry.selectedCategoryId,
              decoration: InputDecoration(
                labelText: '카테고리 *',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 14, color: Colors.black),
              items: categories.map((category) {
                return DropdownMenuItem<int>(
                  value: category.id,
                  child: Text(category.name),
                );
              }).toList(),
              onChanged: (int? value) {
                setState(() {
                  entry.selectedCategoryId = value;
                });
              },
            );
          },
        );
      },
    );
  }

  /// 판매자 드롭다운을 구성합니다.
  Widget _buildSellerDropdown(ProductEntry entry) {
    return Consumer(
      builder: (context, ref, child) {
        final sellerState = ref.watch(sellerNotifierProvider);
        final sellers = sellerState.sellers;

        if (sellers.isEmpty) {
          return Container(
            height: 56,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text(
                '판매자 없음',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          );
        }

        return DropdownButtonFormField<String>(
          value: entry.selectedSeller,
          decoration: InputDecoration(
            labelText: '판매자 *',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            isDense: true,
          ),
          style: const TextStyle(fontSize: 14, color: Colors.black),
          items: sellers.map((seller) {
            final isDefault = seller.isDefault;
            return DropdownMenuItem<String>(
              value: seller.name,
              child: Text(
                isDefault ? '⭐ ${seller.name}' : seller.name,
                style: TextStyle(
                  fontWeight: isDefault ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? value) {
            setState(() {
              entry.selectedSeller = value;
            });
          },
        );
      },
    );
  }





  /// 이미지를 선택하고 크롭합니다 (기존 상품 등록과 동일한 방식).
  Future<void> _selectImage(ProductEntry entry) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        // 1. 흰색 캔버스+중앙 배치 적용
        final originalBytes = await image.readAsBytes();
        final paddedBytes = await addWhitePaddingAndCenterImage(originalBytes);
        final tempDir = await getTemporaryDirectory();
        final paddedPath = path.join(tempDir.path, 'padded_${DateTime.now().millisecondsSinceEpoch}.jpg');
        final paddedFile = await File(paddedPath).writeAsBytes(paddedBytes);

        // 원본 패딩 이미지 경로 저장 (재크롭을 위해)
        entry.originalPaddedImagePath = paddedFile.path;

        // 2. 크롭 다이얼로그(라운드 사각형, 1:1, 오버레이/로딩/원본노출방지)
        final croppedFile = await ImageCropUtils.cropImage(
          context: context,
          imagePath: paddedFile.path,
          shape: CropShape.roundedSquare,
          aspectRatio: 1.0,
        );

        if (croppedFile != null && mounted) {
          setState(() {
            entry.croppedImagePath = croppedFile.path;
          });
        } else {
          // 크롭을 취소한 경우 이미지를 표시하지 않음
          setState(() {
            entry.croppedImagePath = null;
          });
        }
      }
    } catch (e) {
      LoggerUtils.logError('이미지 선택 실패', error: e);
      if (mounted) {
        ToastUtils.showError(context, '이미지 선택에 실패했습니다.');
      }
    }
  }

  /// 일괄 입력 다이얼로그를 표시합니다.
  void _showQuickInputDialog() {
    final priceController = TextEditingController();
    final quantityController = TextEditingController();
    String? selectedSeller;
    int? selectedCategoryId;

    showDialog(
      context: context,
      builder: (context) => Consumer(
        builder: (context, ref, child) {
          final sellerState = ref.watch(sellerNotifierProvider);
          final sellers = sellerState.sellers;
          final categoriesAsync = ref.watch(categoryNotifierProvider);

          return AlertDialog(
            title: const Text('일괄 입력'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('모든 상품에 동일한 값을 적용합니다.'),
                  const SizedBox(height: 16),

                  // 가격 입력
                  TextField(
                    controller: priceController,
                    decoration: const InputDecoration(
                      labelText: '가격',
                      suffixText: '원',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                  ),
                  const SizedBox(height: 16),

                  // 수량 입력
                  TextField(
                    controller: quantityController,
                    decoration: const InputDecoration(
                      labelText: '수량',
                      suffixText: '개',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                  ),
                  const SizedBox(height: 16),

                  // 카테고리 선택
                  categoriesAsync.when(
                    loading: () => const SizedBox(
                      height: 56,
                      child: Center(child: CircularProgressIndicator()),
                    ),
                    error: (error, stack) => Container(
                      height: 56,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.red),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Center(child: Text('카테고리 로드 오류')),
                    ),
                    data: (categories) {
                      if (categories.isEmpty) {
                        return Container(
                          height: 56,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Center(child: Text('카테고리 없음')),
                        );
                      }

                      return DropdownButtonFormField<int>(
                        value: selectedCategoryId,
                        decoration: const InputDecoration(
                          labelText: '카테고리',
                          border: OutlineInputBorder(),
                        ),
                        items: categories.map((category) {
                          return DropdownMenuItem<int>(
                            value: category.id,
                            child: Text(category.name),
                          );
                        }).toList(),
                        onChanged: (int? value) {
                          selectedCategoryId = value;
                        },
                      );
                    },
                  ),
                  const SizedBox(height: 16),

                  // 판매자 선택
                  if (sellers.isNotEmpty)
                    DropdownButtonFormField<String>(
                      value: selectedSeller,
                      decoration: const InputDecoration(
                        labelText: '판매자',
                        border: OutlineInputBorder(),
                      ),
                      items: sellers.map((seller) {
                        final isDefault = seller.isDefault;
                        return DropdownMenuItem<String>(
                          value: seller.name,
                          child: Text(
                            isDefault ? '⭐ ${seller.name}' : seller.name,
                            style: TextStyle(
                              fontWeight: isDefault ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: (String? value) {
                        selectedSeller = value;
                      },
                    ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('취소'),
              ),
              ElevatedButton(
                onPressed: () {
                  _applyQuickInput(
                    priceController.text,
                    quantityController.text,
                    selectedSeller,
                    selectedCategoryId,
                  );
                  Navigator.of(context).pop();
                },
                child: const Text('적용'),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 일괄 입력을 적용합니다.
  void _applyQuickInput(String price, String quantity, String? seller, int? categoryId) {
    setState(() {
      for (final entry in _productEntries) {
        if (price.isNotEmpty) {
          entry.priceController.text = price;
        }
        if (quantity.isNotEmpty) {
          entry.quantityController.text = quantity;
        }
        if (seller != null) {
          entry.selectedSeller = seller;
        }
        if (categoryId != null) {
          entry.selectedCategoryId = categoryId;
        }
      }
    });
  }

  /// 상품들을 등록합니다.
  Future<void> _registerProducts() async {
    if (_isProcessing) return;

    // 현재 워크스페이스 확인
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      ToastUtils.showError(context, '현재 선택된 행사 워크스페이스가 없습니다. 행사 워크스페이스를 선택해주세요.');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final List<Product> productsToAdd = [];
      final List<String> incompleteProducts = [];

      for (int i = 0; i < _productEntries.length; i++) {
        final entry = _productEntries[i];

        // 빈 항목 건너뛰기
        if (entry.nameController.text.trim().isEmpty) {
          continue;
        }

        // 필수 데이터 검증
        bool isIncomplete = false;
        final name = entry.nameController.text.trim();
        final priceText = entry.priceController.text.trim();
        final quantityText = entry.quantityController.text.trim();
        final seller = entry.selectedSeller;
        final categoryId = entry.selectedCategoryId;

        if (priceText.isEmpty) {
          LoggerUtils.logWarning('가격이 입력되지 않음: $name');
          isIncomplete = true;
        }

        if (quantityText.isEmpty) {
          LoggerUtils.logWarning('수량이 입력되지 않음: $name');
          isIncomplete = true;
        }

        if (seller == null || seller.isEmpty) {
          LoggerUtils.logWarning('판매자가 선택되지 않음: $name');
          isIncomplete = true;
        }

        if (categoryId == null) {
          LoggerUtils.logWarning('카테고리가 선택되지 않음: $name');
          isIncomplete = true;
        }

        if (isIncomplete) {
          incompleteProducts.add('${i + 1}. $name');
          continue;
        }

        final price = int.tryParse(priceText) ?? 0;
        final quantity = int.tryParse(quantityText) ?? 0;

        if (price <= 0) {
          LoggerUtils.logWarning('가격이 0 이하: $name');
          incompleteProducts.add('${i + 1}. $name (가격 오류)');
          continue;
        }

        if (quantity <= 0) {
          LoggerUtils.logWarning('수량이 0 이하: $name');
          incompleteProducts.add('${i + 1}. $name (수량 오류)');
          continue;
        }

        // 이미지 저장 (크롭된 이미지 파일 경로 사용)
        final imagePath = entry.croppedImagePath != null
            ? await _saveImageFileToInternalStorage(name, entry.croppedImagePath!)
            : null;

        // 새 상품 생성 (고유한 임시 ID 생성)
        final tempId = DateTime.now().millisecondsSinceEpoch + i; // 고유한 임시 ID
        final product = Product(
          id: tempId, // 임시 ID 설정으로 문서 이름 중복 방지
          name: name,
          quantity: quantity,
          price: price,
          sellerName: seller,
          categoryId: categoryId!,
          imagePath: imagePath,
          lastServicedDate: null,
          isActive: true,

          eventId: currentWorkspace.id,
        );

        productsToAdd.add(product);
        LoggerUtils.logInfo('상품 데이터 준비 완료: $name (가격: $price원, 수량: $quantity개)');
      }

      if (productsToAdd.isEmpty) {
        ToastUtils.showError(context, '등록할 수 있는 상품이 없습니다. 상품 정보를 확인해주세요.');
        return;
      }

      if (incompleteProducts.isNotEmpty) {
        final shouldContinue = await _showIncompleteProductsDialog(incompleteProducts, productsToAdd.length);
        if (!shouldContinue) {
          return;
        }
      }

      // 배치 처리로 상품들을 등록 (성능 최적화)
      int successCount = 0;
      final List<String> failedProducts = [];

      try {
        // 1. 로컬 DB에 배치로 저장
        final repository = ref.read(productRepositoryProvider);
        final batchResult = await repository.batchInsertProducts(productsToAdd);

        successCount = batchResult.succeeded.length;
        failedProducts.addAll(batchResult.failed.map((error) => error.item.name));

        LoggerUtils.logInfo('배치 상품 등록 완료: 성공 $successCount개, 실패 ${batchResult.failed.length}개');

        // 2. Firebase에 배치로 업로드 (백그라운드)
        if (batchResult.succeeded.isNotEmpty) {
          final dataSyncService = ref.read(dataSyncServiceProvider);

          // 각 상품을 개별적으로 Firebase에 업로드 (이미지 포함)
          for (final product in batchResult.succeeded) {
            try {
              await dataSyncService.uploadSingleProduct(product);
              LoggerUtils.logInfo('Firebase 업로드 성공: ${product.name}');
            } catch (e) {
              LoggerUtils.logError('Firebase 업로드 실패 (로컬 저장은 성공): ${product.name}', error: e);
              // Firebase 업로드 실패해도 로컬 저장은 성공했으므로 계속 진행
            }
          }
        }

        // 3. UI 갱신
        await ref.read(productNotifierProvider.notifier).loadProducts();

      } catch (e) {
        LoggerUtils.logError('배치 상품 등록 실패', error: e);
        failedProducts.addAll(productsToAdd.map((p) => p.name));
      }

      final message = failedProducts.isEmpty
          ? '총 ${productsToAdd.length}개 상품이 성공적으로 등록되었습니다.'
          : '총 ${productsToAdd.length}개 중 ${successCount}개 상품이 성공적으로 등록되었습니다. 실패: ${failedProducts.length}개';
      LoggerUtils.logInfo(message);

      // 등록 완료 페이지로 이동
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (context) => RegistrationCompletePage(
              description: message,
              onConfirm: () {
                // POS 화면으로 돌아가기 (모든 스택 제거)
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/',
                  (route) => false,
                );
              },
            ),
          ),
          (route) => false,
        );
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError('상품 대량 등록 중 오류', error: e, stackTrace: stackTrace);
      if (mounted) {
        ToastUtils.showError(context, '상품 등록 중 오류가 발생했습니다: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// 미완성 상품 확인 다이얼로그를 표시합니다.
  Future<bool> _showIncompleteProductsDialog(List<String> incompleteProducts, int validCount) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('미완성 상품 발견'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('다음 상품들은 필수 정보가 누락되어 등록되지 않습니다:'),
            const SizedBox(height: 8),
            ...incompleteProducts.map((product) => Text('• $product')),
            const SizedBox(height: 16),
            Text('완성된 ${validCount}개 상품만 등록하시겠습니까?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('계속 등록'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// 이미지 파일을 내부 저장소에 저장합니다.
  Future<String?> _saveImageFileToInternalStorage(String productName, String croppedImagePath) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir = Directory('${appDir.path}/product_images');

      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }

      final fileName = '${productName}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final targetFile = File('${imagesDir.path}/$fileName');

      // 크롭된 이미지 파일을 내부 저장소로 복사
      final sourceFile = File(croppedImagePath);
      await sourceFile.copy(targetFile.path);

      return targetFile.path;
    } catch (e) {
      LoggerUtils.logError('이미지 저장 실패', error: e);
      return null;
    }
  }
}
