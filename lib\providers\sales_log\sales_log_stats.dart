import '../../models/sales_log.dart';
import '../../models/sales_stat_item.dart';
import '../../models/transaction_type.dart';
import '../../repositories/sales_log_repository.dart';
import '../../utils/logger_utils.dart';
import '../../utils/provider_exception.dart';
import 'sales_log_state.dart';
import '../unified_workspace_provider.dart';
import '../../utils/event_workspace_utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 판매 기록 통계 계산 기능을 담당하는 클래스
///
/// 주요 기능:
/// - 전체 판매 통계 계산
/// - 판매자별 통계 계산
/// - 기간별 통계 계산
/// - 총 판매/할인 금액 계산
class SalesLogStats {
  static const String _tag = 'SalesLogStats';
  static const String _domain = 'SLG_STATS';

  final SalesLogRepository _salesLogRepository;
  final dynamic Function(SalesLogState) _updateState;
  final Ref _ref;

  SalesLogStats({
    required SalesLogRepository salesLogRepository,
    required dynamic Function(SalesLogState) updateState,
    required Ref ref,
  })  : _salesLogRepository = salesLogRepository,
        _updateState = updateState,
        _ref = ref;

  /// 전체 판매 통계 로드
  Future<List<SalesStatItem>> loadOverallSalesStats() async {
    LoggerUtils.methodStart('loadOverallSalesStats', tag: _tag);

    try {
      _updateState(SalesLogState(isUpdating: true));

      // 현재 선택된 행사 확인
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
      final eventId = currentEvent?.id;

      final salesStats = await _salesLogRepository.getOverallSalesStats(eventId: eventId);

      _updateState(SalesLogState(
        salesStats: salesStats,
        isUpdating: false,
      ));

      LoggerUtils.methodEnd('loadOverallSalesStats', tag: _tag);
      return salesStats;
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '전체 판매 통계 로드 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      throw ProviderException(
        message: '전체 판매 통계를 로드하는 중 오류가 발생했습니다',
        code: '${_domain}_LOAD_STATS_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      _updateState(SalesLogState(isUpdating: false));
    }
  }

  /// 총 판매 금액 계산
  Future<int> getTotalSalesAmount({
    int? startTime,
    int? endTime,
    String? sellerName,
  }) async {
    LoggerUtils.methodStart('getTotalSalesAmount', tag: _tag);

    try {
      // 현재 선택된 행사 확인
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
      final eventId = currentEvent?.id;

      if (startTime != null && endTime != null) {
        return await _salesLogRepository.getTotalSalesAmount(
          startTime,
          endTime,
          sellerName,
        );
      } else if (sellerName != null) {
        return await _salesLogRepository.getTotalSalesAmountBySeller(sellerName);
      } else {
        return await _salesLogRepository.getTotalSalesAmountAll(eventId: eventId);
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Error in getTotalSalesAmount',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );

      _updateState(SalesLogState(
        errorMessage: '총 판매 금액을 조회하는 중 오류가 발생했습니다: $e',
      ));
      return 0;
    } finally {
      LoggerUtils.methodEnd('getTotalSalesAmount', tag: _tag);
    }
  }

  /// 총 할인 금액 계산
  Future<int> getTotalDiscountAmount({
    int? startTime,
    int? endTime,
    String? sellerName,
  }) async {
    LoggerUtils.methodStart('getTotalDiscountAmount', tag: _tag);

    try {
      if (startTime != null && endTime != null) {
        return await _salesLogRepository.getTotalDiscountAmount(
          startTime,
          endTime,
          sellerName,
        );
      } else if (sellerName != null) {
        return await _salesLogRepository.getTotalDiscountAmountBySeller(sellerName);
      } else {
        return await _salesLogRepository.getTotalDiscountAmountAll();
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Error in getTotalDiscountAmount',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );

      _updateState(SalesLogState(
        errorMessage: '총 할인 금액을 조회하는 중 오류가 발생했습니다: $e',
      ));
      return 0;
    } finally {
      LoggerUtils.methodEnd('getTotalDiscountAmount', tag: _tag);
    }
  }

  /// 판매자별 통계 계산
  Future<Map<String, SalesStatItem>> calculateSellerStats(
    List<SalesLog> salesLogs,
  ) async {
    LoggerUtils.methodStart('calculateSellerStats', tag: _tag);

    try {
      final Map<String, SalesStatItem> sellerStats = {};

      for (final log in salesLogs) {
        final sellerName = log.sellerName ?? '알 수 없음';
        
        if (!sellerStats.containsKey(sellerName)) {
          sellerStats[sellerName] = SalesStatItem(
            name: sellerName,
            count: 0,
            totalAmount: 0.0,
            totalDiscountAmount: 0.0,
            netSalesAmount: 0.0,
          );
        }

        final stat = sellerStats[sellerName]!;
        
        switch (log.transactionType) {
          case TransactionType.sale:
            sellerStats[sellerName] = stat.copyWith(
              totalAmount: stat.totalAmount + log.totalAmount,
              count: stat.count + 1,
            );
            break;
          case TransactionType.service:
            sellerStats[sellerName] = stat.copyWith(
              totalAmount: stat.totalAmount + log.totalAmount,
              count: stat.count + 1,
            );
            break;
          case TransactionType.discount:
            sellerStats[sellerName] = stat.copyWith(
              totalDiscountAmount: stat.totalDiscountAmount + log.totalAmount,
              count: stat.count + 1,
            );
            break;

          case TransactionType.setDiscount:
            // 세트 할인은 별도 집계 (실제로는 판매에 이미 반영됨)
            sellerStats[sellerName] = stat.copyWith(
              count: stat.count + 1,
            );
            break;
        }
      }

      LoggerUtils.methodEnd('calculateSellerStats', tag: _tag);
      return sellerStats;
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '판매자별 통계 계산 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      return {};
    }
  }

  /// 기간별 통계 계산
  Future<Map<String, SalesStatItem>> calculatePeriodStats(
    List<SalesLog> salesLogs,
    {required DateTime startDate, required DateTime endDate}
  ) async {
    LoggerUtils.methodStart('calculatePeriodStats', tag: _tag);

    try {
      final Map<String, SalesStatItem> periodStats = {};
      final filteredLogs = salesLogs.where((log) {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
        return logDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
               logDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();

      for (final log in filteredLogs) {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
        final dateKey = '${logDate.year}-${logDate.month.toString().padLeft(2, '0')}-${logDate.day.toString().padLeft(2, '0')}';
        
        if (!periodStats.containsKey(dateKey)) {
          periodStats[dateKey] = SalesStatItem(
            name: dateKey,
            count: 0,
            totalAmount: 0.0,
            totalDiscountAmount: 0.0,
            netSalesAmount: 0.0,
          );
        }

        final stat = periodStats[dateKey]!;
        
        switch (log.transactionType) {
          case TransactionType.sale:
            periodStats[dateKey] = stat.copyWith(
              totalAmount: stat.totalAmount + log.totalAmount,
              count: stat.count + 1,
            );
            break;
          case TransactionType.service:
            periodStats[dateKey] = stat.copyWith(
              totalAmount: stat.totalAmount + log.totalAmount,
              count: stat.count + 1,
            );
            break;
          case TransactionType.discount:
            periodStats[dateKey] = stat.copyWith(
              totalDiscountAmount: stat.totalDiscountAmount + log.totalAmount,
              count: stat.count + 1,
            );
            break;

          case TransactionType.setDiscount:
            // 세트 할인은 별도 집계
            periodStats[dateKey] = stat.copyWith(
              count: stat.count + 1,
            );
            break;
        }
      }

      LoggerUtils.methodEnd('calculatePeriodStats', tag: _tag);
      return periodStats;
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '기간별 통계 계산 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      return {};
    }
  }

  /// 거래 타입별 통계 계산
  Future<Map<TransactionType, SalesStatItem>> calculateTypeStats(
    List<SalesLog> salesLogs,
  ) async {
    LoggerUtils.methodStart('calculateTypeStats', tag: _tag);

    try {
      final Map<TransactionType, SalesStatItem> typeStats = {};

      for (final type in TransactionType.values) {
        typeStats[type] = SalesStatItem(
          name: type.displayName,
          count: 0,
          totalAmount: 0.0,
          totalDiscountAmount: 0.0,
          netSalesAmount: 0.0,
        );
      }

      for (final log in salesLogs) {
        final stat = typeStats[log.transactionType]!;
        
        switch (log.transactionType) {
          case TransactionType.sale:
            typeStats[log.transactionType] = stat.copyWith(
              totalAmount: stat.totalAmount + log.totalAmount,
              count: stat.count + 1,
            );
            break;
          case TransactionType.service:
            typeStats[log.transactionType] = stat.copyWith(
              totalAmount: stat.totalAmount + log.totalAmount,
              count: stat.count + 1,
            );
            break;
          case TransactionType.discount:
            typeStats[log.transactionType] = stat.copyWith(
              totalDiscountAmount: stat.totalDiscountAmount + log.totalAmount,
              count: stat.count + 1,
            );
            break;

          case TransactionType.setDiscount:
            // 세트 할인은 별도 로직에서 처리하므로 여기서는 스킵
            break;
        }
      }

      // 세트 할인 통계 별도 계산
      _calculateSetDiscountTypeStats(salesLogs, typeStats);

      LoggerUtils.methodEnd('calculateTypeStats', tag: _tag);
      return typeStats;
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '거래 타입별 통계 계산 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      return {};
    }
  }

  /// 세트 할인 통계 계산 (batchSaleId 기준으로 그룹핑)
  void _calculateSetDiscountTypeStats(
    List<SalesLog> salesLogs,
    Map<TransactionType, SalesStatItem> typeStats,
  ) {
    // 세트 할인이 적용된 로그들만 필터링
    final setDiscountLogs = salesLogs.where((log) => log.setDiscountAmount > 0).toList();

    if (setDiscountLogs.isEmpty) return;

    // batchSaleId별로 그룹핑 (같은 세트 판매)
    final Map<String?, List<SalesLog>> groupedByBatch = {};
    final Set<String> processedSingleItems = {}; // 단일 판매 중복 방지

    for (final log in setDiscountLogs) {
      if (log.batchSaleId != null) {
        // 묶음 판매인 경우
        groupedByBatch.putIfAbsent(log.batchSaleId, () => []).add(log);
      } else {
        // 단일 판매인 경우 (중복 방지)
        final key = '${log.id}_${log.saleTimestamp}';
        if (!processedSingleItems.contains(key)) {
          processedSingleItems.add(key);
          groupedByBatch.putIfAbsent(null, () => []).add(log);
        }
      }
    }

    final setDiscountStat = typeStats[TransactionType.setDiscount]!;
    int totalCount = 0;
    double totalAmount = 0.0;

    // 각 그룹(세트)별로 통계 계산
    for (final group in groupedByBatch.values) {
      if (group.isNotEmpty) {
        // 건수: 1건 (하나의 세트 판매)
        totalCount++;

        // 할인 금액: 해당 세트의 총 할인 금액
        final totalDiscountAmount = group.fold<int>(0, (sum, log) => sum + log.setDiscountAmount);
        totalAmount += totalDiscountAmount.toDouble();
      }
    }

    // 세트 할인 통계 업데이트
    typeStats[TransactionType.setDiscount] = setDiscountStat.copyWith(
      count: totalCount,
      totalAmount: totalAmount,
    );
  }
}