import 'package:intl/intl.dart';

/// 통화/금액 처리 및 포맷팅을 지원하는 유틸리티 클래스입니다.
/// - 원화/달러/유로 등 통화 변환, 금액 포맷, 소수점 처리 등 지원
/// - riverpod 3.x Provider/Repository와 연동, UX 개선 목적
class CurrencyUtils {
  // 한국 원화 포맷터
  static final NumberFormat _currencyFormatter = NumberFormat('#,###', 'ko_KR');
  static final NumberFormat _currencyFormatterWithSymbol =
      NumberFormat.currency(locale: 'ko_KR', symbol: '￦', decimalDigits: 0);

  /// 정수를 원화 형식으로 포맷 (예: 10000 -> "10,000")
  static String formatCurrency(int amount) {
    return _currencyFormatter.format(amount);
  }

  /// 정수를 원화 형식으로 포맷 (원 단위 포함) (예: 10000 -> "₩10,000")
  static String formatCurrencyWithSymbol(int amount) {
    return _currencyFormatterWithSymbol.format(amount);
  }

  /// 정수를 원화 형식으로 포맷 (원 단위 포함) (예: 10000 -> "10,000원")
  static String formatCurrencyWithWon(int amount) {
    return '${formatCurrency(amount)}원';
  }

  /// formatWon 메서드 (기존 코드와의 호환성을 위해)
  static String formatWon(int amount) {
    return formatCurrencyWithWon(amount);
  }

  /// 큰 금액을 간단하게 표시 (예: 1000000 -> "1M", 1000 -> "1K")
  static String formatCurrencyCompact(int amount) {
    if (amount >= 1000000000) {
      return '${(amount / 1000000000).toStringAsFixed(1)}B';
    } else if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toString();
    }
  }

  /// 문자열을 정수로 파싱 (콤마 제거)
  static int? parseCurrency(String currencyString) {
    try {
      // 콤마, 원화 기호, 원 단위 제거
      final cleanString = currencyString
          .replaceAll(',', '')
          .replaceAll('₩', '')
          .replaceAll('원', '')
          .trim();

      if (cleanString.isEmpty) return null;

      return int.parse(cleanString);
    } catch (e) {
      return null;
    }
  }

  /// 양수인지 확인
  static bool isPositive(int amount) {
    return amount > 0;
  }

  /// 음수인지 확인
  static bool isNegative(int amount) {
    return amount < 0;
  }

  /// 0인지 확인
  static bool isZero(int amount) {
    return amount == 0;
  }

  /// 절대값 반환
  static int abs(int amount) {
    return amount.abs();
  }

  /// 두 금액의 합계
  static int add(int amount1, int amount2) {
    return amount1 + amount2;
  }

  /// 두 금액의 차이
  static int subtract(int amount1, int amount2) {
    return amount1 - amount2;
  }

  /// 금액에 퍼센트 적용 (할인율 계산 등)
  static int applyPercentage(int amount, double percentage) {
    return (amount * percentage / 100).round();
  }

  /// 할인 적용된 금액 계산
  static int applyDiscount(int originalAmount, int discountAmount) {
    return originalAmount - discountAmount;
  }

  /// 할인율로 할인 금액 계산
  static int calculateDiscountAmount(
    int originalAmount,
    double discountPercentage,
  ) {
    return applyPercentage(originalAmount, discountPercentage);
  }

  /// 할인율로 최종 금액 계산
  static int calculateDiscountedAmount(
    int originalAmount,
    double discountPercentage,
  ) {
    final discountAmount = calculateDiscountAmount(
      originalAmount,
      discountPercentage,
    );
    return applyDiscount(originalAmount, discountAmount);
  }

  /// 금액 범위 검증
  static bool isInRange(int amount, int minAmount, int maxAmount) {
    return amount >= minAmount && amount <= maxAmount;
  }

  /// 최소 금액 이상인지 확인
  static bool isAboveMinimum(int amount, int minimumAmount) {
    return amount >= minimumAmount;
  }

  /// 최대 금액 이하인지 확인
  static bool isBelowMaximum(int amount, int maximumAmount) {
    return amount <= maximumAmount;
  }

  /// 금액을 천원 단위로 반올림
  static int roundToThousand(int amount) {
    return ((amount / 1000).round() * 1000);
  }

  /// 금액을 만원 단위로 반올림
  static int roundToTenThousand(int amount) {
    return ((amount / 10000).round() * 10000);
  }

  /// 금액 배열의 합계 계산
  static int sum(List<int> amounts) {
    return amounts.fold(0, (sum, amount) => sum + amount);
  }

  /// 금액 배열의 평균 계산
  static double average(List<int> amounts) {
    if (amounts.isEmpty) return 0.0;
    return sum(amounts) / amounts.length;
  }

  /// 금액 배열의 최대값
  static int max(List<int> amounts) {
    if (amounts.isEmpty) return 0;
    return amounts.reduce((a, b) => a > b ? a : b);
  }

  /// 금액 배열의 최소값
  static int min(List<int> amounts) {
    if (amounts.isEmpty) return 0;
    return amounts.reduce((a, b) => a < b ? a : b);
  }

  /// 금액을 색상으로 표시할 때 사용할 색상 결정
  /// 양수: 파란색, 음수: 빨간색, 0: 회색
  static String getAmountColorHex(int amount) {
    if (amount > 0) {
      return '#2196F3'; // 파란색
    } else if (amount < 0) {
      return '#F44336'; // 빨간색
    } else {
      return '#9E9E9E'; // 회색
    }
  }

  /// 금액에 따른 부호 표시
  static String getAmountSign(int amount) {
    if (amount > 0) {
      return '+';
    } else if (amount < 0) {
      return '-';
    } else {
      return '';
    }
  }

  /// 부호와 함께 금액 포맷
  static String formatCurrencyWithSign(int amount) {
    final sign = getAmountSign(amount);
    final absAmount = abs(amount);
    return '$sign${formatCurrencyWithWon(absAmount)}';
  }

  /// 간단한 금액 표시 (만원 단위)
  static String formatSimpleAmount(int amount) {
    if (amount >= 10000) {
      final manWon = amount ~/ 10000;
      final remainder = amount % 10000;
      if (remainder == 0) {
        return '$manWon만원';
      } else {
        return '$manWon만 ${formatCurrency(remainder)}원';
      }
    } else {
      return formatCurrencyWithWon(amount);
    }
  }

  static final _numberFormat = NumberFormat('#,###');

  /// 숫자를 천 단위 구분자가 있는 문자열로 변환합니다.
  static String formatNumber(int number) {
    return _numberFormat.format(number);
  }
}
