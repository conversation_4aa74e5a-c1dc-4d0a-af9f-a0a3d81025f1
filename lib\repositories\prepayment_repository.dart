import 'package:sqflite/sqflite.dart';
import '../models/prepayment.dart';
import '../models/prepayment_sort_order.dart';
import '../services/database_service.dart';
import '../services/event_workspace_manager.dart';
import '../utils/sql_utils.dart';
import '../utils/offline_task.dart';
import '../utils/network_status.dart';

/// 선결제(예약) 데이터의 CRUD, 검색, 상태/정렬/보안 등 관리 기능을 제공하는 Repository 클래스입니다.
/// - 로컬 DB(SQLite)와 연동하며, 오프라인 작업 큐/동기화 구조를 지원합니다.
/// - SQL injection 방지, 구매자/날짜/상태 등 다양한 필터링 지원
class PrepaymentRepository {
  final DatabaseService _databaseService;
  late final Future<Database> _database;

  // 허용된 정렬 컬럼 목록
  static const List<String> _allowedOrderByColumns = [
    'registrationdate',
    'buyername',
    'amount',
  ];

  PrepaymentRepository({required DatabaseService database})
    : _databaseService = database {
    _database = _databaseService.database;
  }

  // 모든 선불결제 목록 조회 (구매자명 오름차순)
  Future<List<Prepayment>> getAllPrepayments() async {
    final db = await _database;

    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.prepaymentsTable,
      where: currentEventId != null ? 'eventId = ?' : null,
      whereArgs: currentEventId != null ? [currentEventId] : null,
      orderBy: 'buyerName ASC',
    );
    return maps.map((map) => Prepayment.fromMap(map)).toList();
  }

  // 특정 행사의 선불결제 목록 조회
  Future<List<Prepayment>> getPrepaymentsByEventId(int eventId) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.prepaymentsTable,
      where: 'eventId = ?',
      whereArgs: [eventId],
      orderBy: 'buyerName ASC',
    );
    return maps.map((map) => Prepayment.fromMap(map)).toList();
  }

  // 정렬된 선불결제 목록 조회
  Future<List<Prepayment>> getPrepaymentsSorted(
    PrepaymentSortOrder sortOrder, {
    int? eventId,
  }) async {
    String orderBy;

    switch (sortOrder) {
      case PrepaymentSortOrder.writtenDateDesc:
        orderBy = SqlUtils.createSafeOrderBy(
          'registrationDate',
          _allowedOrderByColumns,
          descending: true,
        );
        break;
      case PrepaymentSortOrder.writtenDateAsc:
        orderBy = SqlUtils.createSafeOrderBy(
          'registrationDate',
          _allowedOrderByColumns,
        );
        break;
      case PrepaymentSortOrder.buyerNameAsc:
        orderBy = SqlUtils.createSafeOrderBy(
          'buyerName',
          _allowedOrderByColumns,
        );
        break;
      case PrepaymentSortOrder.buyerNameDesc:
        orderBy = SqlUtils.createSafeOrderBy(
          'buyerName',
          _allowedOrderByColumns,
          descending: true,
        );
        break;
      case PrepaymentSortOrder.amountAsc:
        orderBy = SqlUtils.createSafeOrderBy('amount', _allowedOrderByColumns);
        break;
      case PrepaymentSortOrder.amountDesc:
        orderBy = SqlUtils.createSafeOrderBy(
          'amount',
          _allowedOrderByColumns,
          descending: true,
        );
        break;
    }

    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.prepaymentsTable,
      where: eventId != null ? 'eventId = ?' : null,
      whereArgs: eventId != null ? [eventId] : null,
      orderBy: orderBy,
    );
    return maps.map((map) => Prepayment.fromMap(map)).toList();
  }

  // ID로 특정 선불결제 조회
  Future<Prepayment?> getPrepaymentById(int id) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.prepaymentsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Prepayment.fromMap(maps.first);
    }
    return null;
  }

  // 검색 기능
  Future<List<Prepayment>> searchPrepayments(String searchQuery) async {
    if (searchQuery.trim().isEmpty) {
      return await getAllPrepayments();
    }

    final safePattern = SqlUtils.createSafeLikePattern(searchQuery.trim());

    final db = await _database;
    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;

    final whereParts = <String>[
      '''(
        buyerName LIKE ? ESCAPE '\\' OR
        buyerContact LIKE ? ESCAPE '\\' OR
        productNameList LIKE ? ESCAPE '\\'
      )'''
    ];
    final whereArgs = <Object>[safePattern, safePattern, safePattern];
    if (currentEventId != null) {
      whereParts.add('eventId = ?');
      whereArgs.add(currentEventId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.prepaymentsTable,
      where: whereParts.join(' AND '),
      whereArgs: whereArgs,
      orderBy: 'buyerName ASC',
    );
    return maps.map((map) => Prepayment.fromMap(map)).toList();
  }

  // 특정 등록 요일에 해당하는 선불결제 목록 조회
  Future<List<Prepayment>> getPrepaymentsByRegistrationDayOfWeek(
    int dayOfWeek,
  ) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.prepaymentsTable,
      where: 'registrationActualDayOfWeek = ?',
      whereArgs: [dayOfWeek],
      orderBy: 'registrationDate DESC',
    );
    return maps.map((map) => Prepayment.fromMap(map)).toList();
  }

  // 수령 상태별 선불결제 조회
  Future<List<Prepayment>> getPrepaymentsByReceiveStatus(
    bool isReceived,
  ) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.prepaymentsTable,
      where: 'isReceived = ?',
      whereArgs: [isReceived ? 1 : 0],
      orderBy: 'registrationDate DESC',
    );
    return maps.map((map) => Prepayment.fromMap(map)).toList();
  }

  // 특정 날짜 범위의 선불결제 조회
  Future<List<Prepayment>> getPrepaymentsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.prepaymentsTable,
      where: 'registrationTimestamp BETWEEN ? AND ?',
      whereArgs: [
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      ],
      orderBy: 'registrationTimestamp DESC',
    );
    return maps.map((map) => Prepayment.fromMap(map)).toList();
  }

  // 선불결제 삽입
  Future<int> insertPrepayment(Prepayment prepayment) async {
    if (!NetworkStatusUtil.isOnline) {
      final task = OfflineTask(
        id: DateTime.now().microsecondsSinceEpoch.toString(),
        type: OfflineTaskType.insert,
        table: DatabaseServiceImpl.prepaymentsTable,
        data: prepayment.toMap(),
        timestamp: DateTime.now(),
      );
      await (_databaseService as DatabaseServiceImpl).addOfflineTask(
        task.toMap(),
      );
      return -1;
    }
    final db = await _database;
    // registrationDate를 초 단위로 변환
    final regDate = DateTime(
      prepayment.registrationDate.year, prepayment.registrationDate.month, prepayment.registrationDate.day,
      prepayment.registrationDate.hour, prepayment.registrationDate.minute, prepayment.registrationDate.second
    ).toIso8601String();
    // 중복 선입금 방지: 주요 필드 조합으로 현재 행사 내에서만 이미 존재하는지 확인
    final List<Map<String, dynamic>> existing = await db.query(
      DatabaseServiceImpl.prepaymentsTable,
      where: 'buyerName = ? AND buyerContact = ? AND productNameList = ? AND amount = ? AND registrationDate = ? AND eventId = ?',
      whereArgs: [
        prepayment.buyerName,
        prepayment.buyerContact,
        prepayment.productNameList,
        prepayment.amount,
        regDate,
        prepayment.eventId,
      ],
    );
    if (existing.isNotEmpty) {
      // 이미 존재하면 해당 id 반환
      return existing.first['id'] as int;
    }
    return await db.insert(
      DatabaseServiceImpl.prepaymentsTable,
      prepayment.toMap(),
      conflictAlgorithm: ConflictAlgorithm.ignore,
    );
  }

  // 선불결제 업데이트
  Future<int> updatePrepayment(Prepayment prepayment) async {
    if (!NetworkStatusUtil.isOnline) {
      final task = OfflineTask(
        id: DateTime.now().microsecondsSinceEpoch.toString(),
        type: OfflineTaskType.update,
        table: DatabaseServiceImpl.prepaymentsTable,
        data: prepayment.toMap(),
        timestamp: DateTime.now(),
      );
      await (_databaseService as DatabaseServiceImpl).addOfflineTask(
        task.toMap(),
      );
      return -1;
    }
    final db = await _database;
    return await db.update(
      DatabaseServiceImpl.prepaymentsTable,
      prepayment.toMap(),
      where: 'id = ?',
      whereArgs: [prepayment.id],
    );
  }

  // 선불결제 삭제
  Future<int> deletePrepayment(int id) async {
    if (!NetworkStatusUtil.isOnline) {
      final task = OfflineTask(
        id: DateTime.now().microsecondsSinceEpoch.toString(),
        type: OfflineTaskType.delete,
        table: DatabaseServiceImpl.prepaymentsTable,
        data: {'id': id},
        timestamp: DateTime.now(),
      );
      await (_databaseService as DatabaseServiceImpl).addOfflineTask(
        task.toMap(),
      );
      return -1;
    }
    final db = await _database;
    return await db.delete(
      DatabaseServiceImpl.prepaymentsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // 수령 상태 업데이트
  Future<int> updateReceiveStatus(int prepaymentId, bool isReceived) async {
    final db = await _database;
    return await db.update(
      DatabaseServiceImpl.prepaymentsTable,
      {'isReceived': isReceived ? 1 : 0},
      where: 'id = ?',
      whereArgs: [prepaymentId],
    );
  }

  // 모든 선불결제 삭제
  Future<int> deleteAllPrepayments() async {
    final db = await _database;
    return await db.delete(DatabaseServiceImpl.prepaymentsTable);
  }

  // 총 선불결제 금액 계산
  Future<int> getTotalPrepaymentAmount() async {
    final db = await _database;
    final result = await db.rawQuery(
      'SELECT SUM(amount) as total FROM ${DatabaseServiceImpl.prepaymentsTable}',
    );
    return (result.first['total'] as int?) ?? 0;
  }

  // 미수령 선불결제 개수
  Future<int> getUnreceivedCount() async {
    final db = await _database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseServiceImpl.prepaymentsTable} WHERE isReceived = 0',
    );
    return (result.first['count'] as int?) ?? 0;
  }
}
