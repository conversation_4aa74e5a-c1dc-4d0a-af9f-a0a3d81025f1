# Google Play Console 내부 테스트 설정 가이드

## 1. 구글 플레이 콘솔 계정 설정

### 1.1 개발자 계정 등록
1. [Google Play Console](https://play.google.com/console)에 접속
2. Google 계정으로 로그인
3. 개발자 등록비 $25 결제 (일회성)
4. 개발자 계정 정보 입력

### 1.2 앱 생성
1. "모든 앱" → "앱 만들기" 클릭
2. 앱 세부 정보 입력:
   - **앱 이름**: <PERSON>bara (또는 원하는 앱 이름)
   - **기본 언어**: 한국어
   - **앱 또는 게임**: 앱
   - **무료 또는 유료**: 무료
3. 선언 체크박스 모두 체크 후 "앱 만들기" 클릭

## 2. 앱 정보 설정

### 2.1 앱 콘텐츠 설정
1. **개인정보처리방침**: 웹사이트 URL 입력 필요
2. **앱 액세스 권한**: 모든 기능 제한 없음
3. **광고**: 광고 포함 여부 선택
4. **콘텐츠 등급**: 설문조사 완료
5. **타겟 고객층**: 연령대 선택
6. **뉴스 앱**: 해당없음

### 2.2 스토어 설정
1. **주요 스토어 등록정보**:
   - 앱 이름
   - 간단한 설명 (80자 이내)
   - 자세한 설명 (4000자 이내)
2. **스크린샷**: 
   - 휴대전화: 최소 2개 (16:9 또는 9:16 비율)
   - 태블릿: 최소 1개 (선택사항)
3. **아이콘**: 512x512 PNG
4. **기능 그래픽**: 1024x500 PNG

## 3. AAB 업로드 및 내부 테스트 설정

### 3.1 앱 서명
1. **앱 서명** 탭에서 "Google Play 앱 서명" 선택
2. "업로드 키 인증서 업로드" 클릭
3. 생성된 키스토어 정보 입력:
   ```
   키스토어 파일: android/upload-keystore.jks
   키 별칭: upload
   비밀번호: parabara123
   ```

### 3.2 내부 테스트 트랙 생성
1. **테스팅** → **내부 테스팅** 클릭
2. "새 버전 만들기" 클릭
3. AAB 파일 업로드:
   - 파일 위치: `build/app/outputs/bundle/release/app-release.aab`
   - 드래그 앤 드롭 또는 찾아보기로 업로드

### 3.3 버전 정보 입력
1. **버전 이름**: 1.0.0
2. **버전 코드**: 자동 설정됨
3. **출시 노트** (한국어):
   ```
   첫 번째 내부 테스트 버전입니다.
   - 기본 앱 기능 구현
   - Firebase 연동 완료
   - 앱 체크 보안 설정 적용
   ```

### 3.4 테스터 관리
1. **내부 테스팅** → **테스터** 탭
2. "테스터 목록 만들기" 클릭
3. 테스터 그룹 이름 입력 (예: "개발팀")
4. 테스터 이메일 주소 추가:
   - Gmail 계정만 가능
   - 쉼표로 구분하여 여러 명 추가 가능

## 4. 내부 테스트 배포

### 4.1 버전 검토 및 배포
1. 모든 설정 완료 후 "검토" 클릭
2. 오류가 없다면 "내부 테스팅으로 출시" 클릭
3. 처리 시간: 보통 몇 분 ~ 몇 시간

### 4.2 테스트 링크 공유
1. 배포 완료 후 **내부 테스팅** 페이지에서 "테스트 참여 링크 복사"
2. 테스터들에게 링크 공유:
   ```
   https://play.google.com/apps/internaltest/[앱-ID]
   ```

## 5. 테스터 참여 방법

### 5.1 테스터가 해야 할 것
1. Google Play 스토어 앱에 테스터로 등록된 Gmail 계정으로 로그인
2. 공유받은 테스트 링크 클릭
3. "테스트 참여" 버튼 클릭
4. Google Play 스토어에서 앱 다운로드

### 5.2 주의사항
- 테스터는 반드시 Google Play 스토어에 Gmail 계정으로 로그인해야 함
- 내부 테스트는 최대 100명까지 참여 가능
- 앱이 설치된 후에도 테스트 피드백 제공 가능

## 6. 업데이트 배포

### 6.1 새 버전 업로드
1. Flutter에서 새 AAB 빌드:
   ```bash
   flutter build appbundle --release
   ```
2. **내부 테스팅**에서 "새 버전 만들기"
3. 새 AAB 파일 업로드
4. 출시 노트 업데이트
5. "내부 테스팅으로 출시" 클릭

### 6.2 버전 관리
- 버전 코드는 항상 증가해야 함 (자동 처리됨)
- 버전 이름은 의미있게 관리 (예: 1.0.1, 1.1.0)

## 7. 문제 해결

### 7.1 일반적인 오류
1. **키스토어 오류**: 키 정보가 일치하지 않는 경우
2. **권한 오류**: 매니페스트 권한 문제
3. **64비트 요구사항**: 모든 네이티브 라이브러리가 64비트 지원해야 함

### 7.2 테스트 관련 문제
1. **링크 접근 불가**: 테스터 이메일이 올바르게 등록되었는지 확인
2. **앱 다운로드 실패**: Google Play 스토어 계정과 테스터 계정 일치 확인

## 8. 프로덕션 준비

내부 테스트가 완료되면:
1. **비공개 테스트** (알파/베타) 진행
2. **프로덕션 출시** 준비
3. 스토어 등록정보 최종 검토
4. 출시 전 체크리스트 완료

---

## 빌드된 파일 정보
- **AAB 파일**: `build/app/outputs/bundle/release/app-release.aab`
- **파일 크기**: 72.9MB
- **버전**: 1.0.0
- **패키지명**: com.blue.parabara
