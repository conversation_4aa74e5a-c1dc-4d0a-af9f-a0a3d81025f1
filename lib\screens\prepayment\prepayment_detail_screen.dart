import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/prepayment.dart';
import '../../providers/prepayment_provider.dart';
import '../../utils/currency_utils.dart';

import 'register_prepayment_screen.dart';
import '../../utils/date_utils.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/confirmation_dialog.dart';

class PrepaymentDetailScreen extends ConsumerStatefulWidget {
  final int prepaymentId;

  const PrepaymentDetailScreen({super.key, required this.prepaymentId});

  @override
  ConsumerState<PrepaymentDetailScreen> createState() =>
      _PrepaymentDetailScreenState();
}

class _PrepaymentDetailScreenState extends ConsumerState<PrepaymentDetailScreen>
    with RestorationMixin {
  Prepayment? _prepayment;
  bool _isLoading = true;

  @override
  String? get restorationId => 'prepayment_detail_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    _loadPrepayment();
  }

  Future<void> _loadPrepayment() async {
    try {
      final prepayment = await ref
          .read(prepaymentNotifierProvider.notifier)
          .getPrepaymentById(widget.prepaymentId);
      setState(() {
        _prepayment = prepayment;
        _isLoading = false;
      });

      if (prepayment == null) {
        if (mounted) {
          ToastUtils.showError(
            context,
            '선입금 정보를 찾을 수 없습니다.',
          );
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ToastUtils.showError(
          context,
          '데이터 로드 중 오류가 발생했습니다: $e',
        );
      }
    }
  }

  Future<void> _toggleReceivedStatus() async {
    if (_prepayment == null) return;

    try {
      if (_prepayment?.id != null) {
        await ref
            .read(prepaymentNotifierProvider.notifier)
            .toggleReceiveStatus(_prepayment!.id);
      }

      // 상태가 변경된 후 다시 로드
      await _loadPrepayment();

      if (mounted) {
        ToastUtils.showToast(
          context,
          _prepayment!.isReceived ? '수령 완료로 변경되었습니다.' : '미수령으로 변경되었습니다.',
          duration: ToastUtils.shortDuration,
        );
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(
          context,
          '상태 변경 중 오류가 발생했습니다: $e',
        );
      }
    }
  }

  Future<void> _editPrepayment() async {
    if (_prepayment == null) return;

    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => RegisterPrepaymentScreen(prepaymentId: _prepayment!.id),
      ),
    );

    if (result == true) {
      // invalidate를 사용한 즉시 상태 갱신
      ref.invalidate(prepaymentNotifierProvider);
    }
  }

  Future<void> _deletePrepayment() async {
    if (_prepayment == null) return;

    final confirmed = await ConfirmationDialog.showDelete(
      context: context,
      title: '삭제 확인',
      message: '\'${_prepayment!.buyerName}\'님의 선입금 정보를 정말 삭제하시겠습니까?',
      confirmLabel: '삭제',
      cancelLabel: '취소',
    );

    if (confirmed == true) {
      try {
        if (_prepayment?.id != null) {
          await ref
              .read(prepaymentNotifierProvider.notifier)
              .deletePrepayment(_prepayment!.id);
        }

        if (mounted) {
          ToastUtils.showToast(
            context,
            '선입금 정보가 삭제되었습니다.',
            duration: ToastUtils.shortDuration,
          );
          Navigator.of(context).pop();
        }
      } catch (e) {
        if (mounted) {
          ToastUtils.showError(
            context,
            '삭제 중 오류가 발생했습니다: $e',
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (_prepayment == null) {
      return const Scaffold(body: Center(child: Text('선입금 정보를 찾을 수 없습니다.')));
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('${_prepayment!.buyerName}님의 선입금'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editPrepayment,
            tooltip: '수정',
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deletePrepayment,
            tooltip: '삭제',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 구매자 정보
            _buildSectionTitle('구매자 정보'),
            const SizedBox(height: 8),
            _buildInfoRow('구매자명', _prepayment!.buyerName),
            _buildInfoRow('연락처', _prepayment!.buyerContact),
            _buildInfoRow('이메일', _prepayment!.email),
            if (_prepayment!.twitterAccount?.isNotEmpty == true)
              _buildInfoRow('트위터', _prepayment!.twitterAccount!),

            const SizedBox(height: 24),

            // 결제 정보
            _buildSectionTitle('결제 정보'),
            const SizedBox(height: 8),
            _buildInfoRow(
              '결제 금액',
              CurrencyUtils.formatCurrency(_prepayment!.amount),
            ),
            _buildInfoRow('입금 계좌', _prepayment!.bankName),

            const SizedBox(height: 24),

            // 수령 정보
            _buildSectionTitle('수령 정보'),
            const SizedBox(height: 8),
            _buildInfoRow(
              '수령 요일',
              _prepayment!.pickupDays.join(', '),
            ),
            
            // 상품 정보 개선
            if (_prepayment!.purchasedProducts.isNotEmpty) ...[
              _buildInfoRow('구매 상품', ''),
              ..._prepayment!.purchasedProducts.map((product) => 
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 4, bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 100,
                        child: Text(
                          '• ${product.name}',
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontWeight: FontWeight.w500),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text('${product.quantity}개'),
                    ],
                  ),
                ),
              ),
            ],
            
            if (_prepayment!.memo?.isNotEmpty == true)
              _buildInfoRow('메모', _prepayment!.memo!),

            const SizedBox(height: 24),

            // 등록 정보
            _buildSectionTitle('등록 정보'),
            const SizedBox(height: 8),
            _buildInfoRow(
              '등록일',
              AppDateUtils.formatKorDateTime(_prepayment!.registrationDate),
            ),
            _buildInfoRow('수령 상태', _prepayment!.isReceived ? '수령 완료' : '미수령'),
            if (_prepayment!.orderNumber?.isNotEmpty == true)
              _buildInfoRow('주문번호', _prepayment!.orderNumber!),

            const SizedBox(height: 32),

            // 수령 상태 변경 버튼
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _toggleReceivedStatus,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _prepayment!.isReceived
                      ? Colors.grey
                      : Colors.blue,
                ),
                child: Text(_prepayment!.isReceived ? '미수령으로 변경' : '수령 완료로 변경'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 18, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(fontFamily: 'Pretendard', 
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(child: Text(value, style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 16))),
        ],
      ),
    );
  }
}




