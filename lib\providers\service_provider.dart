import 'package:flutter_riverpod/flutter_riverpod.dart';


import '../models/sales_log.dart';
import '../models/transaction_type.dart';
import '../providers/base_state.dart';
import '../providers/product_provider.dart' as product_provider;
import '../providers/sales_log_provider.dart';
import '../providers/unified_workspace_provider.dart';
import '../repositories/product_repository.dart';
import '../repositories/sales_log_repository.dart' as sales_log_repo;
import '../utils/logger_utils.dart';
import '../utils/provider_error_handler.dart';

/// 서비스 처리 상태
class ServiceState extends BaseState {
  final bool isProcessing;
  final Map<int, int> serviceQuantities;

  const ServiceState({
    this.isProcessing = false,
    this.serviceQuantities = const {},
    super.isLoading = false,
    super.errorMessage,
    super.errorCode,
    super.errorSeverity,
    super.errorDetails,
    super.isCancelled,
  });

  @override
  List<Object?> get props => [
        isProcessing,
        serviceQuantities,
        ...super.props,
      ];

  ServiceState copyWith({
    bool? isProcessing,
    Map<int, int>? serviceQuantities,
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return ServiceState(
      isProcessing: isProcessing ?? this.isProcessing,
      serviceQuantities: serviceQuantities ?? this.serviceQuantities,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      errorSeverity: errorSeverity ?? this.errorSeverity,
      errorDetails: errorDetails ?? this.errorDetails,
      isCancelled: isCancelled ?? this.isCancelled,
    );
  }

  @override
  BaseState copyWithBase({
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return copyWith(
      isLoading: isLoading,
      errorMessage: errorMessage,
      errorCode: errorCode,
      errorSeverity: errorSeverity,
      errorDetails: errorDetails,
      isCancelled: isCancelled,
    );
  }
}

/// 서비스 처리 Notifier
class ServiceNotifier extends StateNotifier<ServiceState> {
  final sales_log_repo.SalesLogRepository _salesLogRepository;
  final ProductRepository _productRepository;
  final Ref _ref;
  static const String _tag = 'ServiceNotifier';
  bool _isPaused = false;

  ServiceNotifier(
    this._salesLogRepository,
    this._productRepository,
    this._ref,
  ) : super(const ServiceState());

  /// Provider 일시 정지
  void pause() {
    _isPaused = true;
    LoggerUtils.logInfo('ServiceNotifier 일시 정지됨', tag: _tag);
  }

  /// Provider 재개
  void resume() {
    _isPaused = false;
    LoggerUtils.logInfo('ServiceNotifier 재개됨', tag: _tag);
  }

  /// 서비스 처리 실행
  /// 
  /// [serviceQuantities]: 상품 ID와 서비스 수량의 맵
  Future<void> processService(Map<int, int> serviceQuantities) async {
    if (_isPaused) return;

    LoggerUtils.methodStart('processService', tag: _tag);
    LoggerUtils.logDebug('서비스 처리 요청: $serviceQuantities', tag: _tag);

    await ProviderErrorHandler.executeCrudOperation<ServiceState>(
      operation: () async {
        state = state.copyWith(isProcessing: true);

        try {
          // 1. 유효성 검사
          await _validateServiceRequest(serviceQuantities);

          // 2. 현재 상품 정보 가져오기
          final productState = _ref.read(product_provider.productNotifierProvider);
          final products = productState.products;

          // 3. 현재 행사 워크스페이스 확인
          final currentWorkspace = _ref.read(currentWorkspaceProvider);
          if (currentWorkspace == null) {
            throw Exception('행사 워크스페이스를 선택해주세요.');
          }

          // 4. 각 상품에 대해 서비스 처리
          final currentTimestamp = DateTime.now().millisecondsSinceEpoch;
          final batchServiceId = 'service_$currentTimestamp';

          for (final entry in serviceQuantities.entries) {
            final productId = entry.key;
            final serviceQuantity = entry.value;

            if (serviceQuantity <= 0) continue;

            final product = products.firstWhere(
              (p) => p.id == productId,
              orElse: () => throw Exception('상품을 찾을 수 없습니다: ID $productId'),
            );

            // 재고 확인
            if (product.quantity < serviceQuantity) {
              throw Exception('${product.name}: 재고가 부족합니다. (요청: $serviceQuantity, 재고: ${product.quantity})');
            }

            // 4. 재고 차감 - 직접 decreaseStock 메서드 사용으로 중복 Firebase 업로드 방지
            await _productRepository.decreaseStock(productId, serviceQuantity);

            // 5. 판매 기록 생성 (금액 0, 서비스 타입)
            // 고유 ID 생성 - microsecond + 추가 고유성 보장
            final uniqueId = DateTime.now().microsecondsSinceEpoch + 
                           (productId * 1000) + 
                           serviceQuantity; // 더 강한 고유성 보장
            
            final salesLog = SalesLog(
              id: uniqueId,
              productId: productId,
              productName: '[서비스] ${product.name}', // 서비스 접두사 추가
              sellerName: product.sellerName,
              soldPrice: 0, // 서비스는 금액 0
              soldQuantity: serviceQuantity,
              totalAmount: 0, // 서비스는 총액 0
              saleTimestamp: currentTimestamp,
              transactionType: TransactionType.service, // 서비스 타입
              batchSaleId: serviceQuantities.length > 1 ? batchServiceId : null,
              eventId: currentWorkspace.id, // 현재 행사 ID 설정
            );

            await _salesLogRepository.addSalesLog(salesLog);

            LoggerUtils.logInfo(
              '서비스 처리 완료: ${product.name} x$serviceQuantity',
              tag: _tag,
            );
          }

          // 6. 상품 목록 새로고침
          await _ref.read(product_provider.productNotifierProvider.notifier).loadProducts(showLoading: false);

          LoggerUtils.logInfo('모든 서비스 처리 완료', tag: _tag);
        } finally {
          state = state.copyWith(isProcessing: false);
        }
      },
      notifier: this,
      errorCode: 'SERVICE_PROCESS_ERROR',
      refreshData: () async {
        // 에러 발생 시 상품 목록 새로고침
        await _ref.read(product_provider.productNotifierProvider.notifier).loadProducts(showLoading: false);
      },
      tag: _tag,
      operationName: '서비스 처리',
    );

    LoggerUtils.methodEnd('processService', tag: _tag);
  }

  /// 서비스 요청 유효성 검사
  Future<void> _validateServiceRequest(Map<int, int> serviceQuantities) async {
    LoggerUtils.methodStart('_validateServiceRequest', tag: _tag);

    if (serviceQuantities.isEmpty) {
      throw Exception('서비스 처리할 상품이 없습니다.');
    }

    // 음수나 0인 수량 제거
    final validQuantities = Map<int, int>.from(serviceQuantities);
    validQuantities.removeWhere((key, value) => value <= 0);

    if (validQuantities.isEmpty) {
      throw Exception('유효한 서비스 수량이 없습니다.');
    }

    // 상품 존재 여부 및 재고 확인
    final productState = _ref.read(product_provider.productNotifierProvider);
    final products = productState.products;

    for (final entry in validQuantities.entries) {
      final productId = entry.key;
      final serviceQuantity = entry.value;

      final product = products.where((p) => p.id == productId).firstOrNull;
      if (product == null) {
        throw Exception('상품을 찾을 수 없습니다: ID $productId');
      }

      if (!product.isActive) {
        throw Exception('${product.name}: 비활성 상품은 서비스 처리할 수 없습니다.');
      }

      if (product.quantity < serviceQuantity) {
        throw Exception('${product.name}: 재고가 부족합니다. (요청: $serviceQuantity, 재고: ${product.quantity})');
      }
    }

    LoggerUtils.methodEnd('_validateServiceRequest', tag: _tag);
  }

  /// 수량 업데이트
  void updateServiceQuantity(int productId, int quantity) {
    if (_isPaused) return;

    final updatedQuantities = Map<int, int>.from(state.serviceQuantities);
    
    if (quantity <= 0) {
      updatedQuantities.remove(productId);
    } else {
      updatedQuantities[productId] = quantity;
    }

    state = state.copyWith(serviceQuantities: updatedQuantities);
  }

  /// 모든 수량 초기화
  void clearAllQuantities() {
    if (_isPaused) return;
    
    state = state.copyWith(serviceQuantities: {});
  }

  /// 특정 상품 수량 초기화
  void clearProductQuantity(int productId) {
    if (_isPaused) return;

    final updatedQuantities = Map<int, int>.from(state.serviceQuantities);
    updatedQuantities.remove(productId);
    state = state.copyWith(serviceQuantities: updatedQuantities);
  }

  /// 로딩 상태 설정
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }
}

/// ServiceNotifier Provider
final serviceNotifierProvider = StateNotifierProvider<ServiceNotifier, ServiceState>((ref) {
  final salesLogRepository = ref.watch(salesLogRepositoryProvider);
  final productRepository = ref.watch(product_provider.productRepositoryProvider);
  return ServiceNotifier(salesLogRepository, productRepository, ref);
});

/// 서비스 처리 중 여부 Provider
final serviceIsProcessingProvider = Provider<bool>((ref) {
  return ref.watch(serviceNotifierProvider).isProcessing;
});

/// 서비스 수량 맵 Provider
final serviceQuantitiesProvider = Provider<Map<int, int>>((ref) {
  return ref.watch(serviceNotifierProvider).serviceQuantities;
});

/// 서비스 로딩 상태 Provider
final serviceIsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(serviceNotifierProvider).isLoading;
});

/// 서비스 에러 메시지 Provider
final serviceErrorMessageProvider = Provider<String?>((ref) {
  return ref.watch(serviceNotifierProvider).errorMessage;
});

/// 서비스 에러 코드 Provider
final serviceErrorCodeProvider = Provider<String?>((ref) {
  return ref.watch(serviceNotifierProvider).errorCode;
});

/// 서비스 데이터 동기화 관리자
class ServiceDataSyncManager {
  static const String _tag = 'ServiceDataSyncManager';

  /// 모든 서비스 관련 Provider를 일관되게 갱신
  static Future<void> syncAllServiceData(WidgetRef ref) async {
    LoggerUtils.logInfo('서비스 데이터 동기화 시작', tag: _tag);

    try {
      // 1. 상품 데이터 갱신
      await ref.read(product_provider.productNotifierProvider.notifier).loadProducts();

      // 2. 서비스 관련 Provider 갱신
      ref.invalidate(serviceNotifierProvider);
      ref.invalidate(serviceIsProcessingProvider);
      ref.invalidate(serviceQuantitiesProvider);
      ref.invalidate(serviceIsLoadingProvider);
      ref.invalidate(serviceErrorMessageProvider);
      ref.invalidate(serviceErrorCodeProvider);

      LoggerUtils.logInfo('서비스 데이터 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('서비스 데이터 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 서비스 처리 후 동기화
  static Future<void> syncAfterServiceProcess(WidgetRef ref) async {
    LoggerUtils.logInfo('서비스 처리 후 동기화 시작', tag: _tag);

    try {
      // 1. 상품 데이터 갱신 (재고 변경 반영)
      await ref.read(product_provider.productNotifierProvider.notifier).loadProducts(showLoading: false);

      // 2. 서비스 상태 초기화
      ref.read(serviceNotifierProvider.notifier).clearAllQuantities();

      LoggerUtils.logInfo('서비스 처리 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('서비스 처리 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
}
