import 'package:flutter/material.dart';
import 'app_colors.dart';

/// 온보딩 테마에 맞는 다이얼로그 스타일을 제공하는 유틸리티 클래스
class DialogTheme {
  DialogTheme._();

  /// 기본 다이얼로그 데코레이션
  static BoxDecoration get dialogDecoration => BoxDecoration(
    gradient: AppColors.backgroundGradient,
    borderRadius: BorderRadius.circular(24),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadow,
        blurRadius: 24,
        offset: const Offset(0, 8),
      ),
    ],
  );

  /// 다이얼로그 제목 스타일
  static TextStyle get titleStyle => TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.onboardingTextPrimary,
    fontFamily: 'Pretendard',
  );

  /// 다이얼로그 내용 스타일
  static TextStyle get contentStyle => TextStyle(
    fontSize: 16,
    color: AppColors.onboardingTextSecondary,
    height: 1.5,
    fontFamily: 'Pretendard',
  );

  /// 확인 버튼 스타일
  static ButtonStyle get confirmButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: Colors.transparent,
    foregroundColor: AppColors.onboardingTextOnPrimary,
    elevation: 0,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
    ),
  ).copyWith(
    backgroundColor: WidgetStateProperty.all(Colors.transparent),
  );

  /// 취소 버튼 스타일
  static ButtonStyle get cancelButtonStyle => OutlinedButton.styleFrom(
    foregroundColor: AppColors.onboardingTextSecondary,
    side: BorderSide(color: AppColors.secondary),
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
    ),
  );

  /// 삭제 버튼 스타일
  static ButtonStyle get deleteButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: Colors.transparent,
    foregroundColor: AppColors.onboardingTextOnDark,
    elevation: 0,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
    ),
  ).copyWith(
    backgroundColor: WidgetStateProperty.all(Colors.transparent),
  );

  /// 확인 버튼 컨테이너 데코레이션
  static BoxDecoration get confirmButtonDecoration => BoxDecoration(
    gradient: AppColors.primaryGradient,
    borderRadius: BorderRadius.circular(16),
    boxShadow: [
      BoxShadow(
        color: AppColors.onboardingPrimary.withValues(alpha: 0.4),
        blurRadius: 8,
        offset: const Offset(0, 4),
      ),
    ],
  );

  /// 삭제 버튼 컨테이너 데코레이션
  static BoxDecoration get deleteButtonDecoration => BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        AppColors.error,
        AppColors.errorLight,
      ],
    ),
    borderRadius: BorderRadius.circular(16),
    boxShadow: [
      BoxShadow(
        color: AppColors.error.withValues(alpha: 0.4),
        blurRadius: 8,
        offset: const Offset(0, 4),
      ),
    ],
  );

  /// 아이콘 컨테이너 데코레이션
  static BoxDecoration getIconDecoration(Color color) => BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        color.withValues(alpha: 0.2),
        color.withValues(alpha: 0.1),
      ],
    ),
    borderRadius: BorderRadius.circular(16),
  );

  /// 모던한 다이얼로그 래퍼 (컴팩트 버전)
  static Widget buildModernDialog({
    required Widget child,
    double? width,
    double? height,
    bool isCompact = true,
  }) {
    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      insetPadding: EdgeInsets.symmetric(
        horizontal: isCompact ? 24.0 : 40.0,
        vertical: isCompact ? 16.0 : 24.0,
      ),
      child: Container(
        width: width,
        height: height,
        constraints: BoxConstraints(
          maxWidth: isCompact ? 380 : 400,
          minWidth: 280,
        ),
        decoration: dialogDecoration,
        child: child,
      ),
    );
  }

  /// 모던한 바텀시트 래퍼
  static Widget buildModernBottomSheet({
    required Widget child,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppColors.backgroundGradient,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 16,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: child,
    );
  }

  /// 버튼 래퍼 (그라데이션 적용) - 컴팩트 버전
  static Widget buildGradientButton({
    required Widget child,
    required VoidCallback? onPressed,
    required BoxDecoration decoration,
    ButtonStyle? style,
    bool isCompact = true,
  }) {
    return Container(
      decoration: onPressed != null ? decoration : null,
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(isCompact ? 12 : 16),
        child: InkWell(
          borderRadius: BorderRadius.circular(isCompact ? 12 : 16),
          onTap: onPressed,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: isCompact ? 20 : 24,
              vertical: isCompact ? 10 : 12,
            ),
            child: child,
          ),
        ),
      ),
    );
  }

  /// 2024-2025 트렌드에 맞는 모던 버튼 위젯
  static Widget buildModernButton({
    required String text,
    required VoidCallback? onPressed,
    required bool isTablet,
    required bool isPrimary,
    bool isDestructive = false,
  }) {
    Color buttonColor;
    Color textColor;
    Color? borderColor;
    List<BoxShadow>? shadows;

    if (isDestructive) {
      // 모던한 삭제 버튼 - 부드러운 빨간색 톤
      buttonColor = const Color(0xFFFF6B6B); // 더 부드럽고 모던한 빨간색
      textColor = Colors.white;
      shadows = [
        BoxShadow(
          color: const Color(0xFFFF6B6B).withValues(alpha: 0.25),
          blurRadius: 8.0,
          offset: const Offset(0, 2),
        ),
      ];
    } else if (isPrimary) {
      // 기본 Primary 버튼
      buttonColor = AppColors.onboardingPrimary;
      textColor = AppColors.onboardingTextOnPrimary;
      shadows = [
        BoxShadow(
          color: AppColors.onboardingPrimary.withValues(alpha: 0.2),
          blurRadius: 8.0,
          offset: const Offset(0, 2),
        ),
      ];
    } else {
      // Secondary 버튼
      buttonColor = Colors.transparent;
      textColor = AppColors.onboardingTextSecondary;
      borderColor = AppColors.secondary.withValues(alpha: 0.6);
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      height: isTablet ? 48.0 : 44.0,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(14.0),
          onTap: onPressed,
          child: Container(
            decoration: BoxDecoration(
              color: buttonColor,
              border: borderColor != null
                ? Border.all(color: borderColor, width: 1.5)
                : null,
              borderRadius: BorderRadius.circular(14.0),
              boxShadow: shadows,
            ),
            child: Center(
              child: Text(
                text,
                style: TextStyle(
                  color: textColor,
                  fontSize: isTablet ? 16.0 : 14.0,
                  fontWeight: isPrimary || isDestructive ? FontWeight.w600 : FontWeight.w500,
                  fontFamily: 'Pretendard',
                  letterSpacing: 0.2,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 컴팩트한 패딩 값들
  static EdgeInsets getCompactPadding(bool isTablet) => EdgeInsets.all(isTablet ? 20.0 : 16.0);
  static EdgeInsets getCompactDialogPadding(bool isTablet) => EdgeInsets.all(isTablet ? 24.0 : 18.0);

  /// 컴팩트한 간격 값들
  static double getCompactSpacing(bool isTablet) => isTablet ? 12.0 : 8.0;
  static double getCompactSectionSpacing(bool isTablet) => isTablet ? 16.0 : 12.0;
  static double getCompactIconSize(bool isTablet) => isTablet ? 32.0 : 24.0;

  /// 컴팩트한 아이콘 컨테이너
  static Widget buildCompactIconContainer({
    required IconData icon,
    required Color color,
    required bool isTablet,
  }) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 12.0 : 8.0),
      decoration: getIconDecoration(color),
      child: Icon(
        icon,
        size: getCompactIconSize(isTablet),
        color: color,
      ),
    );
  }
}
