import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:convert'; // jsonEncode 추가

import '../../models/prepayment.dart';
import '../../models/purchased_product.dart';
import '../../providers/prepayment_provider.dart';
import '../../providers/prepayment_virtual_product_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/toast_utils.dart';

class RegisterPrepaymentScreen extends ConsumerStatefulWidget {
  final int? prepaymentId;

  const RegisterPrepaymentScreen({super.key, this.prepaymentId});

  @override
  ConsumerState<RegisterPrepaymentScreen> createState() =>
      _RegisterPrepaymentScreenState();
}

class _RegisterPrepaymentScreenState
    extends ConsumerState<RegisterPrepaymentScreen>
    with RestorationMixin {
  final _formKey = GlobalKey<FormState>();
  final _buyerNameController = ValueNotifier<TextEditingController>(TextEditingController());
  final _buyerContactController = ValueNotifier<TextEditingController>(TextEditingController());
  final _amountController = ValueNotifier<TextEditingController>(TextEditingController());
  final _bankNameController = ValueNotifier<TextEditingController>(TextEditingController());
  final _emailController = ValueNotifier<TextEditingController>(TextEditingController());
  final _twitterAccountController = ValueNotifier<TextEditingController>(TextEditingController());
  final _memoController = ValueNotifier<TextEditingController>(TextEditingController());

  // FocusNode 추가
  final FocusNode _buyerNameFocusNode = FocusNode();
  final FocusNode _buyerContactFocusNode = FocusNode();
  final FocusNode _amountFocusNode = FocusNode();
  final FocusNode _bankNameFocusNode = FocusNode();
  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _twitterAccountFocusNode = FocusNode();
  final FocusNode _memoFocusNode = FocusNode();

  Prepayment? _currentPrepayment;
  Set<String> _selectedDays = {};
  List<String> _availableDays = [];
  bool _isLoading = false;
  
  // 가상 상품 선택 방식으로 변경
  List<PurchasedProduct> _selectedProducts = [];

  @override
  String? get restorationId => 'register_prepayment_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {
    // ValueNotifier는 직접 registerForRestoration할 수 없으므로 제거
  }

  @override
  void initState() {
    super.initState();
    // provider 상태 변경은 빌드 이후에 실행
    Future.microtask(() => _loadData());
  }

  @override
  void dispose() {
    _buyerNameController.value.dispose();
    _buyerContactController.value.dispose();
    _amountController.value.dispose();
    _bankNameController.value.dispose();
    _emailController.value.dispose();
    _twitterAccountController.value.dispose();
    _memoController.value.dispose();
    
    // FocusNode 해제
    _buyerNameFocusNode.dispose();
    _buyerContactFocusNode.dispose();
    _amountFocusNode.dispose();
    _bankNameFocusNode.dispose();
    _emailFocusNode.dispose();
    _twitterAccountFocusNode.dispose();
    _memoFocusNode.dispose();
    
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 가상 상품 데이터 로드 (빌드 이후 안전하게 실행)
      await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
      
      // 기본 요일 목록 설정
      _availableDays = [
        '월요일',
        '화요일', 
        '수요일',
        '목요일',
        '금요일',
        '토요일',
        '일요일',
        Prepayment.noDayOfWeek, // '없음' 옵션 추가
      ];

      // 수정 모드인 경우 선입금 정보 로드
      if (widget.prepaymentId != null) {
        final prepayment = await ref
            .read(prepaymentNotifierProvider.notifier)
            .getPrepaymentById(widget.prepaymentId!);
        if (prepayment != null) {
          _currentPrepayment = prepayment;
          _populateUI(prepayment);
        }
      }

      // 기본 요일 설정 (새 등록 시) - '없음'으로 설정
      if (_selectedDays.isEmpty && _availableDays.isNotEmpty) {
        _selectedDays.add(Prepayment.noDayOfWeek);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('데이터 로드 중 오류가 발생했습니다: $e')));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _populateUI(Prepayment prepayment) {
    _buyerNameController.value.text = prepayment.buyerName;
    _buyerContactController.value.text = prepayment.buyerContact;
    _amountController.value.text = prepayment.amount.toString();
    _bankNameController.value.text = prepayment.bankName;
    _emailController.value.text = prepayment.email;
    _twitterAccountController.value.text = prepayment.twitterAccount ?? '';
    _memoController.value.text = prepayment.memo ?? '';

    // 요일 설정 (다중 선택)
    _selectedDays = Set.from(prepayment.pickupDays);
    
    // 상품 목록 설정 (purchasedProducts 사용)
    _selectedProducts = List.from(prepayment.purchasedProducts);
  }
  
  String _buildProductListText() {
    // 상품명만 콤마로 반환 (수량, 단위 제외)
    return _selectedProducts.map((product) => product.name).join(', ');
  }

  String _buildPurchasedProductsJson() {
    return jsonEncode(_selectedProducts.map((e) => e.toJson()).toList());
  }

  Future<void> _showProductSelectionDialog() async {
    final result = await showDialog<List<PurchasedProduct>>(
      context: context,
      builder: (context) => const ProductSelectionDialog(),
    );
    
    if (result != null) {
      setState(() {
        _selectedProducts = result;
      });
    }
  }
  
  void _addProductItem() {
    setState(() {
      _selectedProducts.add(PurchasedProduct(
        name: '',
        quantity: 1,
        price: 0,
      ));
    });
  }
  
  void _removeProductItem(int index) {
    if (_selectedProducts.length > 1) {
      setState(() {
        _selectedProducts.removeAt(index);
      });
    }
  }
  
  void _updateProductItem(int index, String field, dynamic value) {
    setState(() {
      final product = _selectedProducts[index];
      switch (field) {
        case 'name':
          _selectedProducts[index] = product.copyWith(name: value);
          break;
        case 'quantity':
          _selectedProducts[index] = product.copyWith(quantity: value);
          break;
      }
    });
  }

  Future<void> _savePrepayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedDays.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('수령 요일을 선택해주세요.')));
      return;
    }

    // 상품 선택은 선택사항으로 변경

    setState(() {
      _isLoading = true;
    });

    try {
      final buyerName = _buyerNameController.value.text.trim();
      final buyerContact = _buyerContactController.value.text.trim();
      final amount = int.parse(_amountController.value.text.trim());
      final productNameList = _buildProductListText();
      final purchasedProductsJson = _buildPurchasedProductsJson();
      final bankName = _bankNameController.value.text.trim();
      final email = _emailController.value.text.trim();
      final twitterAccount = _twitterAccountController.value.text.trim();
      final memo = _memoController.value.text.trim();

      int registrationActualDayOfWeek = 8; // 기본값: 없음
      final selectedDaysList = _selectedDays.toList();
      if (selectedDaysList.length == 1 && selectedDaysList.first == Prepayment.noDayOfWeek) {
        registrationActualDayOfWeek = 8;
      } else if (selectedDaysList.contains(Prepayment.noDayOfWeek)) {
        registrationActualDayOfWeek = 8;
      } else if (selectedDaysList.isNotEmpty) {
        final idx = Prepayment.availableDaysOfWeek.indexOf(selectedDaysList.first);
        if (idx >= 0 && idx < 7) {
          registrationActualDayOfWeek = idx + 1;
        }
      }

      if (_currentPrepayment == null) {
        // 새 선입금 등록
        final newPrepayment = Prepayment(
          id: 0,
          buyerName: buyerName,
          buyerContact: buyerContact,
          amount: amount,
          pickupDays: _selectedDays.toList(),
          productNameList: productNameList,
          purchasedProductsJson: purchasedProductsJson,
          bankName: bankName,
          email: email,
          twitterAccount: twitterAccount.isNotEmpty ? twitterAccount : null,
          memo: memo.isNotEmpty ? memo : null,
          registrationDate: DateTime.now(),
          isReceived: false,
          registrationActualDayOfWeek: registrationActualDayOfWeek,
          registrationTimestamp: DateTime.now().millisecondsSinceEpoch,
        );

        await ref
            .read(prepaymentNotifierProvider.notifier)
            .addPrepayment(newPrepayment);

        // 가상 상품 데이터 갱신 - 즉시 반영되도록 수정
        await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();

        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('선입금이 등록되었습니다.')));
        }
      } else {
        // 기존 선입금 수정
        final updatedPrepayment = _currentPrepayment!.copyWith(
          buyerName: buyerName,
          buyerContact: buyerContact,
          amount: amount,
          pickupDays: _selectedDays.toList(),
          productNameList: productNameList,
          purchasedProductsJson: purchasedProductsJson,
          bankName: bankName,
          email: email,
          twitterAccount: twitterAccount.isNotEmpty ? twitterAccount : null,
          memo: memo.isNotEmpty ? memo : null,
          registrationActualDayOfWeek: registrationActualDayOfWeek,
        );

        await ref
            .read(prepaymentNotifierProvider.notifier)
            .updatePrepayment(updatedPrepayment);

        // 가상 상품 데이터 갱신 - 즉시 반영되도록 수정
        await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();

        if (mounted) {
          ToastUtils.showSuccess(
            context,
            '선입금 정보가 수정되었습니다.',
          );
        }
      }

      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(
          context,
          '저장 중 오류가 발생했습니다: $e',
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _currentPrepayment == null ? '선입금 등록' : '선입금 수정',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.onPrimary,
          ),
        ),
        backgroundColor: AppColors.primarySeed,
        elevation: 0,
        scrolledUnderElevation: 1,
        shadowColor: AppColors.elevation1,
        actions: [
          IconButton(
            onPressed: _isLoading ? null : _savePrepayment,
            icon: Icon(
              Icons.check,
              color: _isLoading ? Colors.grey : AppColors.onPrimary,
            ),
            tooltip: '저장',
          ),
        ],
      ),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [


                      // 구매자 이름과 금액을 가로로 배치
                      Row(
                        children: [
                          Expanded(
                            child: _buildModernTextField(
                              controller: _buyerNameController.value,
                              focusNode: _buyerNameFocusNode,
                              label: '구매자 이름',
                              hint: '이름을 입력하세요',
                              icon: Icons.person,
                              textInputAction: TextInputAction.next,
                              onFieldSubmitted: (value) => _amountFocusNode.requestFocus(),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return '구매자 이름을 입력해주세요';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildModernTextField(
                              controller: _amountController.value,
                              focusNode: _amountFocusNode,
                              label: '선입금 금액',
                              hint: '0',
                              icon: Icons.attach_money,
                              keyboardType: TextInputType.number,
                              textInputAction: TextInputAction.next,
                              onFieldSubmitted: (value) => _buyerContactFocusNode.requestFocus(),
                              suffixText: '원',
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return '금액을 입력해주세요';
                                }
                                if (int.tryParse(value) == null) {
                                  return '올바른 금액을 입력해주세요';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // 연락처
                      _buildModernTextField(
                        controller: _buyerContactController.value,
                        focusNode: _buyerContactFocusNode,
                        label: '연락처',
                        hint: '전화번호를 입력하세요',
                        icon: Icons.phone,
                        keyboardType: TextInputType.phone,
                        textInputAction: TextInputAction.next,
                        onFieldSubmitted: (value) => _emailFocusNode.requestFocus(),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '연락처를 입력해주세요';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // 이메일 (선택)
                      _buildModernTextField(
                        controller: _emailController.value,
                        focusNode: _emailFocusNode,
                        label: '이메일 (선택)',
                        hint: '<EMAIL>',
                        icon: Icons.email,
                        keyboardType: TextInputType.emailAddress,
                        textInputAction: TextInputAction.next,
                        onFieldSubmitted: (value) => _bankNameFocusNode.requestFocus(),
                        validator: null, // 선택사항이므로 유효성 검사 없음
                      ),
                      const SizedBox(height: 16),

                      // 은행명과 트위터를 가로로 배치
                      Row(
                        children: [
                          Expanded(
                            child: _buildModernTextField(
                              controller: _bankNameController.value,
                              focusNode: _bankNameFocusNode,
                              label: '은행명 (선택)',
                              hint: '입금 은행을 입력하세요',
                              icon: Icons.account_balance,
                              textInputAction: TextInputAction.next,
                              onFieldSubmitted: (value) => _twitterAccountFocusNode.requestFocus(),
                              validator: null, // 선택사항이므로 유효성 검사 없음
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildModernTextField(
                              controller: _twitterAccountController.value,
                              focusNode: _twitterAccountFocusNode,
                              label: '트위터 (선택)',
                              hint: '@username',
                              icon: Icons.alternate_email,
                              textInputAction: TextInputAction.done,
                              validator: null, // 선택사항이므로 유효성 검사 없음
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),

                    // 상품 목록 (가상 상품 선택 방식)
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  '상품 목록',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Row(
                                  children: [
                                    TextButton.icon(
                                      onPressed: _showProductSelectionDialog,
                                      icon: const Icon(Icons.list),
                                      label: const Text('상품 선택'),
                                    ),
                                    IconButton(
                                      onPressed: _addProductItem,
                                      icon: const Icon(Icons.add),
                                      tooltip: '상품 추가',
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            if (_selectedProducts.isEmpty)
                              const Padding(
                                padding: EdgeInsets.all(16.0),
                                child: Center(
                                  child: Text(
                                    '상품을 선택하거나 추가해주세요',
                                    style: TextStyle(color: Colors.grey),
                                  ),
                                ),
                              )
                            else
                              ..._selectedProducts.asMap().entries.map((entry) {
                                final index = entry.key;
                                final product = entry.value;
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        flex: 2,
                                        child: TextFormField(
                                          initialValue: product.name,
                                          decoration: const InputDecoration(
                                            labelText: '상품명',
                                            border: OutlineInputBorder(),
                                          ),
                                          onChanged: (value) => _updateProductItem(index, 'name', value),
                                          validator: (value) {
                                            if (value == null || value.trim().isEmpty) {
                                              return '상품명을 입력하세요';
                                            }
                                            return null;
                                          },
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: TextFormField(
                                          initialValue: product.quantity.toString(),
                                          decoration: const InputDecoration(
                                            labelText: '수량',
                                            border: OutlineInputBorder(),
                                          ),
                                          keyboardType: TextInputType.number,
                                          onChanged: (value) => _updateProductItem(index, 'quantity', int.tryParse(value) ?? 1),
                                          validator: (value) {
                                            if (value == null || value.trim().isEmpty) {
                                              return '수량을 입력하세요';
                                            }
                                            if (int.tryParse(value) == null || int.parse(value) <= 0) {
                                              return '올바른 수량을 입력하세요';
                                            }
                                            return null;
                                          },
                                        ),
                                      ),
                                      if (_selectedProducts.length > 1)
                                        IconButton(
                                          onPressed: () => _removeProductItem(index),
                                          icon: const Icon(Icons.remove),
                                          tooltip: '상품 제거',
                                        ),
                                    ],
                                  ),
                                );
                              }).toList(),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                      // 픽업 요일 선택 (가로 배치)
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: AppColors.neutral60.withValues(alpha: 0.3)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '수령 요일 (복수 선택 가능)',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppColors.onSurface.withValues(alpha: 0.8),
                              ),
                            ),
                            const SizedBox(height: 12),
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: _availableDays.map((day) {
                                final isSelected = _selectedDays.contains(day);
                                return FilterChip(
                                  label: Text(
                                    day,
                                    style: TextStyle(
                                      color: isSelected
                                          ? AppColors.onPrimary
                                          : AppColors.onSurface,
                                      fontWeight: isSelected
                                          ? FontWeight.w600
                                          : FontWeight.normal,
                                    ),
                                  ),
                                  selected: isSelected,
                                  showCheckmark: false, // 체크마크 제거
                                  onSelected: (bool selected) {
                                    setState(() {
                                      if (selected) {
                                        // '없음'이 선택되면 다른 모든 선택 해제
                                        if (day == Prepayment.noDayOfWeek) {
                                          _selectedDays.clear();
                                          _selectedDays.add(day);
                                        } else {
                                          // 다른 요일이 선택되면 '없음' 제거
                                          _selectedDays.remove(Prepayment.noDayOfWeek);
                                          _selectedDays.add(day);
                                        }
                                      } else {
                                        _selectedDays.remove(day);
                                        // 아무것도 선택되지 않으면 '없음' 자동 선택
                                        if (_selectedDays.isEmpty) {
                                          _selectedDays.add(Prepayment.noDayOfWeek);
                                        }
                                      }
                                    });
                                  },
                                  selectedColor: AppColors.primarySeed,
                                  backgroundColor: AppColors.surface,
                                  side: BorderSide(
                                    color: isSelected
                                        ? AppColors.primarySeed
                                        : AppColors.neutral60.withValues(alpha: 0.3),
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),

                      // 메모 (제일 아래로 이동)
                      _buildModernTextField(
                        controller: _memoController.value,
                        focusNode: _memoFocusNode,
                        label: '메모 (선택)',
                        hint: '추가 정보를 입력하세요',
                        icon: Icons.note,
                        maxLines: 3,
                        textInputAction: TextInputAction.done,
                        validator: null, // 선택사항이므로 유효성 검사 없음
                      ),
                      const SizedBox(height: 100), // 하단 버튼 공간 확보

                      // 저장 버튼
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _savePrepayment,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primarySeed,
                            foregroundColor: AppColors.onPrimary,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            _isLoading ? '저장 중...' : (_currentPrepayment == null ? '선입금 등록' : '선입금 수정'),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
      );
    }



  /// 모던한 텍스트 필드 위젯 빌더
  Widget _buildModernTextField({
    required TextEditingController controller,
    FocusNode? focusNode,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    TextInputAction? textInputAction,
    Function(String)? onFieldSubmitted,
    String? Function(String?)? validator,
    String? suffixText,
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      focusNode: focusNode,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      onFieldSubmitted: onFieldSubmitted,
      maxLines: maxLines,
      inputFormatters: keyboardType == TextInputType.number
          ? [FilteringTextInputFormatter.digitsOnly]
          : null,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        suffixText: suffixText,
        prefixIcon: Icon(icon, color: AppColors.primarySeed.withValues(alpha: 0.7)),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.neutral60.withValues(alpha: 0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.neutral60.withValues(alpha: 0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primarySeed, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: AppColors.surface,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        labelStyle: TextStyle(
          color: AppColors.onSurface.withValues(alpha: 0.7),
          fontSize: 14,
        ),
        hintStyle: TextStyle(
          color: AppColors.onSurface.withValues(alpha: 0.5),
          fontSize: 14,
        ),
      ),
      style: TextStyle(
        color: AppColors.onSurface,
        fontSize: 16,
      ),
      validator: validator,
    );
  }
  }

  // 가상 상품 선택 다이얼로그
  class ProductSelectionDialog extends ConsumerStatefulWidget {
    const ProductSelectionDialog({Key? key}) : super(key: key);

    @override
    ConsumerState<ProductSelectionDialog> createState() => _ProductSelectionDialogState();
}

class _ProductSelectionDialogState extends ConsumerState<ProductSelectionDialog> {
  List<PurchasedProduct> _selectedProducts = [];
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadVirtualProducts();
  }

  Future<void> _loadVirtualProducts() async {
    await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
  }

  @override
  Widget build(BuildContext context) {
    final virtualProducts = ref.watch(prepaymentVirtualProductNotifierProvider).virtualProducts;
    final filteredProducts = virtualProducts.where((product) =>
        product.name.toLowerCase().contains(_searchQuery.toLowerCase())).toList();

    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '상품 선택',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 검색 필드
            TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                labelText: '상품 검색',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            const SizedBox(height: 16),
            
            // 상품 목록
            Expanded(
              child: ListView.builder(
                itemCount: filteredProducts.length,
                itemBuilder: (context, index) {
                  final product = filteredProducts[index];
                  final isSelected = _selectedProducts.any((p) => p.name == product.name);
                  
                  return ListTile(
                    title: Text(product.name),
                    subtitle: Text('총 판매 수: ${product.quantity}개'),
                    trailing: isSelected
                        ? const Icon(Icons.check_circle, color: Colors.green)
                        : const Icon(Icons.radio_button_unchecked),
                    onTap: () {
                      setState(() {
                        if (isSelected) {
                          _selectedProducts.removeWhere((p) => p.name == product.name);
                        } else {
                          _selectedProducts.add(PurchasedProduct(
                            name: product.name,
                            quantity: 1,
                            price: product.price,
                          ));
                        }
                      });
                    },
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            
            // 선택된 상품 목록
            if (_selectedProducts.isNotEmpty) ...[
              const Text(
                '선택된 상품',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                height: 100,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  itemCount: _selectedProducts.length,
                  itemBuilder: (context, index) {
                    final product = _selectedProducts[index];
                    return ListTile(
                      title: Text(product.name),
                      subtitle: Text('수량: ${product.quantity}개'),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            onPressed: () {
                              setState(() {
                                if (product.quantity > 1) {
                                  _selectedProducts[index] = product.copyWith(
                                    quantity: product.quantity - 1,
                                  );
                                }
                              });
                            },
                            icon: const Icon(Icons.remove),
                          ),
                          Text('${product.quantity}'),
                          IconButton(
                            onPressed: () {
                              setState(() {
                                _selectedProducts[index] = product.copyWith(
                                  quantity: product.quantity + 1,
                                );
                              });
                            },
                            icon: const Icon(Icons.add),
                          ),
                          IconButton(
                            onPressed: () {
                              setState(() {
                                _selectedProducts.removeAt(index);
                              });
                            },
                            icon: const Icon(Icons.delete),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // 버튼들
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('취소'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(_selectedProducts),
                  child: const Text('확인'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
